
#ifndef __GNET_EC_SQLCREATEARENAPLAYER_HPP
#define __GNET_EC_SQLCREATEARENAPLAYER_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "ec_sqlcreatearenaplayerarg"
#include "ec_sqlcreatearenaplayerres"

#include "ec_arenamanager.h"

namespace GNET
{

class EC_SQLCreateArenaPlayer : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "ec_sqlcreatearenaplayer"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// EC_SQLCreateArenaPlayerArg *arg = (EC_SQLCreateArenaPlayerArg *)argument;
		// EC_SQLCreateArenaPlayerRes *res = (EC_SQLCreateArenaPlayerRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		EC_SQLCreateArenaPlayerArg *arg = (EC_SQLCreateArenaPlayerArg *)argument;
		EC_SQLCreateArenaPlayerRes *res = (EC_SQLCreateArenaPlayerRes *)result;
		ArenaOfAuroraManager::GetInstance()->EC_CreateArenaPlayer_Re(arg->roleid, res->retcode, res->player);
	}

	void OnTimeout()
	{
		Log::log(LOG_ERR,"EC_SQLCreateArenaPlayer: timeout.\n");
	}

};

};
#endif
