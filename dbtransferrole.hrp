
#ifndef __GNET_DBTRANSFERROLE_HPP
#define __GNET_DBTRANSFERROLE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbtransferrolearg"
#include "dbtransferroleres"

namespace GNET
{

class DBTransferRole : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbtransferrole"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBTransferRoleArg *arg = (DBTransferRoleArg *)argument;
		// DBTransferRoleRes *res = (DBTransferRoleRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// DBTransferRoleArg *arg = (DBTransferRoleArg *)argument;
		// DBTransferRoleRes *res = (DBTransferRoleRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
