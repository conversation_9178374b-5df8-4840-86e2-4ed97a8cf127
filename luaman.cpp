//---------------------------------------------------------------------------------------------------------------------------
//--PW LUA SCRIPT GDELIVERYD (C) DeadRaky 2022
//---------------------------------------------------------------------------------------------------------------------------
#include <utf.h>
#include "mapuser.h"
#include "rpcdefs.h"
#include "luaman.hpp"
#include "maplinkserver.h"
#include "gdeliveryserver.hpp"
#include "disconnectplayer.hpp"
#include "chatsinglecast.hpp"
#include "chatbroadcast.hpp"
#include "playerluainfo.hpp"
#include <curl/curl.h>
#include <LuaBridge.h>
#include <string>
using namespace luabridge;

namespace GNET
{ 
	LuaManager* LuaManager::instance = 0;
	lua_State* LuaManager::L;
	pthread_mutex_t LuaManager::lua_mutex;
	const char * LuaManager::FName;
	time_t LuaManager::reload_tm = 0;
	size_t LuaManager::tick = 0;
	bool LuaManager::lua_debug = false;
	LuaManager::CONFIG LuaManager::config;

	time_t LuaManager::GetFileTime(const char *path)
	{
		struct stat statbuf;
		if (stat(path, &statbuf) == -1) {
			perror(path);
			exit(1);
		}
		return statbuf.st_mtime;
	}

	bool LuaManager::IsTrue(int it, int * table)
	{
		for (unsigned int i = 0; table[i] && i < 128 ; i++)
			if (table[i] == it)
				return true;
		return false;
	}

	bool LuaManager::GetNum(const char* s, double& v)
	{
		LuaRef out = getGlobal(L, s);
		if (!out.isNil() && out.isNumber())
		{
			v = out;
			return true;
		}
		printf("LuaManager::GET_NUM: error %s not found! \n", s);
		return false;
	}

	bool LuaManager::GetString(const char* s, const char* v)
	{
		LuaRef out = getGlobal(L, s);
		if (!out.isNil() && out.isString())
		{
			v = out;
			return true;
		}
		printf("LuaManager::GET_STR: error %s not found! \n", s);
		return false;
	}

	bool LuaManager::SetNum(const char* s, double v)
	{
		LuaRef out = getGlobal(L, s);
		if (!out.isNil() && out.isNumber())
		{
			setGlobal(L, v, s);
			return true;
		}
		printf("LuaManager::SET_NUM: error %s not fdound! \n", s);
		return false;
	}

	bool LuaManager::SetString(const char* s, const char* v)
	{
		LuaRef out = getGlobal(L, s);
		if (!out.isNil() && out.isString())
		{
			setGlobal(L, v, s);
			return true;
		}
		printf("LuaManager::SET_STR: error %s not found! \n", s);
		return false;
	}

	bool LuaManager::SetAddr(const char* s, long long v)
	{
		LuaRef out = getGlobal(L, s);
		if (!out.isNil() && out.isNumber())
		{
			setGlobal(L, v, s);
			return true;
		}
		printf("LuaManager::SET_ADDR: error %s not found! \n", s);
		return false;
	}

	void LuaManager::game__Patch(long long address, int type, double value)
	{
		if (address)
		{
			switch (type)
			{
			case TYPE_CHAR: { *(char*)address = value; return; break; }
			case TYPE_SHORT: { *(short*)address = value; return; break; }
			case TYPE_INT: { *(int*)address = value; return; break; }
			case TYPE_INT64: { *(long long*)address = value; return; break; }
			case TYPE_FLOAT: { *(float*)address = value; return; break; }
			case TYPE_DOUBLE: { *(double*)address = value; return; break; }
			default: { printf("game__Patch: ERROR TYPE %d ! \n", type); return; break; }
			}
		}
	}

	double LuaManager::game__Get(long long address, int type, long long offset)
	{
		if (address)
		{
			switch (type)
			{
			case TYPE_CHAR: { return *(char*)(&((char*)address)[offset]); break; }
			case TYPE_SHORT: { return *(short*)(&((char*)address)[offset]); break; }
			case TYPE_INT: { return *(int*)(&((char*)address)[offset]); break; }
			case TYPE_INT64: { return *(long long*)(&((char*)address)[offset]); break; }
			case TYPE_FLOAT: { return *(float*)(&((char*)address)[offset]); break; }
			case TYPE_DOUBLE: { return *(double*)(&((char*)address)[offset]); break; }
			default: { printf("game__Get: ERROR TYPE %d ! \n", type);   return 0; break; }
			}
		}
		return 0;
	}

	void LuaManager::game__SingleChatMsg(int roleid, int channel, const char * utf8_msg)
	{
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRoleOnline(roleid);
		size_t utf8_size = strlen(utf8_msg);
		if (pinfo && utf8_size && utf8_size < 256)
		{
			Octets msg;
			msg.resize(utf8_size*2 + 2);
			memset(msg.begin(), 0x00, msg.size());
			utf8_to_utf16((const utf8_t*)utf8_msg, utf8_size, (utf16_t*)msg.begin(), msg.size() );
			ChatSingleCast csc(channel,0,0, pinfo->roleid, pinfo->localsid, msg, 0 );
			GDeliveryServer::GetInstance()->Send(pinfo->linksid,csc);
		}
	}

	void LuaManager::game__ChatBroadCast(int roleid, int channel, const char * utf8_msg)
	{
		size_t utf8_size = strlen(utf8_msg);
		if (utf8_size && utf8_size < 256)
		{
			Octets msg;
			msg.resize(utf8_size*2 + 2);
			memset(msg.begin(), 0x00, msg.size());
			utf8_to_utf16((const utf8_t*)utf8_msg, utf8_size, (utf16_t*)msg.begin(), msg.size() );
			WorldChat chat(channel,0, roleid, 0, msg, 0);
			LinkServer::GetInstance().BroadcastProtocol(&chat);
		}
	}

	const char * LuaManager::game__GetName(int id)
	{
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRoleOnline(id);
		if (pinfo)
		{
			Octets name = pinfo->name;
			if(name.size() > 0)
			{
				static std::string utf8;
				utf8.clear();
				utf8.resize(name.size() + 1);
				memset((void*)utf8.c_str(), 0x00, utf8.size());
				utf16_to_utf8((const char16_t*)name.begin(), (char*)utf8.c_str(), utf8.size() - 1);
				return (const char *)utf8.c_str();
			}
		}
		else
		{
			return "NULL";
		}	
	}

	void LuaManager::game__RoleLogout(int roleid, bool cross)
	{
		GDeliveryServer* lsm = GDeliveryServer::GetInstance();
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRoleOnline(roleid);
		if (pinfo)
		{
			//UserInfo* user = pinfo->user;
			//UserContainer::GetInstance().RoleLogout(user, cross);
			lsm->Send(pinfo->linksid, DisconnectPlayer( roleid, -1, pinfo->localsid, 1 ));
			UserContainer::GetInstance().UserLogout(pinfo->user);
		}
	}

	void LuaManager::game__RoleLogin(int roleid)
	{
		int userid = UidConverter::Instance().Roleid2Uid(roleid);

		//printf("Lua::RoleLogin: roleid = %d , userid = %d \n", roleid, userid);

		UserInfo* pUser = UserContainer::GetInstance().FindUser(userid);
		if (pUser)
		{
			UserContainer::GetInstance().RoleLogin(pUser, roleid);
		}
	
	}

	std::string LuaManager::game__GetUserIp(int roleid)
	{
		int userid = UidConverter::Instance().Roleid2Uid(roleid);
		UserInfo* pUser = UserContainer::GetInstance().FindUser(userid);
		if (pUser)
		{
			return UserContainer::GetInstance().GetUserIP(pUser->userid);
		}
		return "NULL";
	}

	unsigned long long LuaManager::game__GetHwid(int roleid)
	{
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRole(roleid);
		if (pinfo)
		{
			Octets hwid = pinfo->hwid;
			if (hwid.size() == 8 && *(unsigned long long*)hwid.begin())
			{
				return *(unsigned long long*)hwid.begin();
			}
		}
	}

	double LuaManager::game__GetRoleVar(int type , int roleid)
	{
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRoleOnline(roleid);
		if (pinfo)
		{
			switch (type)
			{
			case PIMP_CLS        	: return pinfo->cls;		break;	
			case PIMP_LEVEL      	: return pinfo->level;		break;	
			case PIMP_USERID     	: return pinfo->userid;		break;
			case PIMP_GM 		 	: return pinfo->IsGM();  	break;	
			case PIMP_IP    		: return pinfo->user->ip;	break;
			case PIMP_GAMEID 		: return pinfo->gameid; 	break;
			default: break;		
			}
		}
		return 0;		
	}

	int LuaManager::game__CheckRoleGsArena(int roleid)
	{
		bool res = 0; 
		PlayerInfo* pUser = UserContainer::GetInstance().FindRoleOnline(roleid);
		if(pUser)
		{
			res = config.IS_TRUE_TAG_ARENA( pUser->user->gameid);
			//res = 
			if(lua_debug) printf("Lua::CheckRoleGsArena: gs = %d , roleid = %d , res = %d \n", pUser->user->gameid , roleid , res );
		}
		return res;
	}

	int LuaManager::game__CheckRoleHideTag(int roleid)
	{
		bool res = 0; 
		PlayerInfo* pUser = UserContainer::GetInstance().FindRoleOnline(roleid);
		if(pUser)
		{
			res = config.IS_TRUE_HIDE_TAG( pUser->user->gameid);
			//res = 
			if(lua_debug) printf("Lua::CheckRoleHideTag: gs = %d , roleid = %d , res = %d \n", pUser->user->gameid , roleid , res );
		}
		return res;
	}

	void LuaManager::sendDiscordWebHookHelper(const std::string& webhook, const std::string& message) 
	{
		CURL *curl = curl_easy_init();
		if(curl) 
		{
			std::string data = "{\"content\": \"" + message + "\"}";
			curl_easy_setopt(curl, CURLOPT_URL, webhook.c_str());
			curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
			curl_easy_setopt(curl, CURLOPT_HTTPHEADER, curl_slist_append(nullptr, "Content-Type: application/json"));
			curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L); // Definindo o timeout como 10 segundos

			CURLcode res = curl_easy_perform(curl);
			if(res != CURLE_OK) {
				//fprintf(stderr, "curl_easy_perform() failed: %s\n", curl_easy_strerror(res));
			}

			curl_easy_cleanup(curl);
		}
	}

	void LuaManager::game__SendDiscordWebHook(const std::string& webhook, const std::string& message) 
	{
		// std::thread sendThread(&LuaManager::sendDiscordWebHookHelper, webhook, message);
		// sendThread.detach(); // Desanexa a thread para que ela execute independentemente
		pid_t pid = fork();
    	if (pid == 0) 
    	{
        	// Child process
        	sendDiscordWebHookHelper(webhook, message);
        	_exit(0);  // Exit child process
    	}
    	else if (pid > 0) 
    	{
        	// Parent process - continue immediately
        	// Child will be reaped automatically
    	}
    	else 
    	{
        	// Fork failed
        	fprintf(stderr, "Fork failed for Discord webhook\n");
    	}
	}

	void LuaManager::sendDiscordEmbedWebHookHelper(const std::string& webhook, const std::string& username,const std::string& avatar_url,const std::string& title,const std::string& message,const std::string& color,const std::string& thumbnail_url,const std::string& footer_url,const std::string& footer_text,const std::string& timestamp)
	{
		CURL *curl = curl_easy_init();
		if(curl) 
		{
			std::string data = R"({
						"username": ")" + username + R"(",
						"avatar_url": ")" + avatar_url + R"(",
						"content": null,
						"embeds": [{
							"title": ")" + title + R"(",
							"description": ")" + message + R"(",
							"color": )" + color + R"(,
							"thumbnail": {
								"url": ")" + thumbnail_url + R"("
							},
							"footer": {
								"icon_url": ")" + footer_url + R"(",
								"text": ")" + footer_text + R"("
							},
							"timestamp": ")" + timestamp + R"("
						}]
					})";

			curl_easy_setopt(curl, CURLOPT_URL, webhook.c_str());
			curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
			curl_easy_setopt(curl, CURLOPT_HTTPHEADER, curl_slist_append(nullptr, "Content-Type: application/json"));
			curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L); // Definindo o timeout como 10 segundos

			CURLcode res = curl_easy_perform(curl);
			if(res != CURLE_OK) {
				//fprintf(stderr, "curl_easy_perform() failed: %s\n", curl_easy_strerror(res));
			}

			curl_easy_cleanup(curl);
		}
	}	

	void LuaManager::game__SendDiscordEmbedWebHook(const std::string& webhook, const std::string& username,const std::string& avatar_url,const std::string& title,const std::string& message,const std::string& color,const std::string& thumbnail_url,const std::string& footer_url,const std::string& footer_text,const std::string& timestamp)
	{
		// std::thread sendThread(&LuaManager::sendDiscordEmbedWebHookHelper, webhook, username, avatar_url, title, message, color, thumbnail_url, footer_url, footer_text, timestamp);
		// sendThread.detach();
		
    pid_t pid = fork();
    if (pid == 0) 
    {
        // Child process
        sendDiscordEmbedWebHookHelper(webhook, username, avatar_url, title, message, color, thumbnail_url, footer_url, footer_text, timestamp);
        _exit(0);  // Exit child process
    }
    else if (pid > 0) 
    {
        // Parent process - continue immediately
        // Child will be reaped automatically
    }
    else 
    {
        // Fork failed
        fprintf(stderr, "Fork failed for Discord embed webhook\n");
    }
	}

	void LuaManager::FunctionsRegister()
	{
		getGlobalNamespace(L).addFunction("game__Patch",game__Patch);
		getGlobalNamespace(L).addFunction("game__Get",game__Get);
		getGlobalNamespace(L).addFunction("game__SingleChatMsg",game__SingleChatMsg);
		getGlobalNamespace(L).addFunction("game__ChatBroadCast",game__ChatBroadCast);
		getGlobalNamespace(L).addFunction("game__CheckRoleGsArena",game__CheckRoleGsArena);
		getGlobalNamespace(L).addFunction("game__CheckRoleHideTag",game__CheckRoleHideTag);
		getGlobalNamespace(L).addFunction("game__SendDiscordWebHook",game__SendDiscordWebHook);
		getGlobalNamespace(L).addFunction("game__SendDiscordEmbedWebHook",game__SendDiscordEmbedWebHook);
		getGlobalNamespace(L).addFunction("game__GetName",game__GetName);
		getGlobalNamespace(L).addFunction("game__GetRoleVar",game__GetRoleVar);
		getGlobalNamespace(L).addFunction("game__RoleLogout",game__RoleLogout);
		getGlobalNamespace(L).addFunction("game__RoleLogin",game__RoleLogin);
		getGlobalNamespace(L).addFunction("game__GetUserIp",game__GetUserIp);
		getGlobalNamespace(L).addFunction("game__GetHwid",game__GetHwid);
	}

	void LuaManager::SetItem()
	{
		SetAddr("HIDE_TAGS"				, (long long)config.HIDE_TAGS );
		SetAddr("ARENA_TAGS"			, (long long)config.ARENA_TAGS );
		SetAddr("HW_TAGS"				, (long long)config.HW_TAGS    );
		SetAddr("COUNTRY_PLAYERS_CNT"	, (long long)config.COUNTRY_PLAYERS_CNT );
	}

	void LuaManager::GetItem()
	{
		double res = -1;
		GetNum( "lua_debug"				, res ) ? lua_debug						= res  : res == -1;
		GetNum( "COUNTRY_MAX_BONUS"		, res ) ? config.COUNTRY_MAX_BONUS		= res  : res == -1;	

		GetNum( "COUNTRYBATTLE_SUNDAY"		, res ) ? config.COUNTRYBATTLE_SUNDAY		= res  : res == -1;
		GetNum( "COUNTRYBATTLE_MONDAY"		, res ) ? config.COUNTRYBATTLE_MONDAY		= res  : res == -1;
		GetNum( "COUNTRYBATTLE_TUESDAY"		, res ) ? config.COUNTRYBATTLE_TUESDAY		= res  : res == -1;
		GetNum( "COUNTRYBATTLE_WEDNESDAY"	, res ) ? config.COUNTRYBATTLE_WEDNESDAY	= res  : res == -1;
		GetNum( "COUNTRYBATTLE_THURSDAY"	, res ) ? config.COUNTRYBATTLE_THURSDAY	= res  : res == -1;
		GetNum( "COUNTRYBATTLE_FRIDAY"		, res ) ? config.COUNTRYBATTLE_FRIDAY		= res  : res == -1;
		GetNum( "COUNTRYBATTLE_SATURDAY"	, res ) ? config.COUNTRYBATTLE_SATURDAY	= res  : res == -1;


		GetNum( "ARENA_MSG_TIME"		, res ) ? config.ARENA_MSG_TIME			= res  : res == -1;
		GetNum( "ARENA_WHITE_TIME"		, res ) ? config.ARENA_WHITE_TIME		= res  : res == -1;
		GetNum( "ARENA_SEARCH_TEAM_TIME"		, res ) ? config.ARENA_SEARCH_TEAM_TIME		= res  : res == -1;
		GetNum( "ARENA_UPDATE_TIME"		, res ) ? config.ARENA_UPDATE_TIME		= res  : res == -1;		
		GetNum( "ARENA_BATTLE_TIME"		, res ) ? config.ARENA_BATTLE_TIME		= res  : res == -1;
		GetNum( "ARENA_CHECK_OPEN_TIME"		, res ) ? config.ARENA_CHECK_OPEN_TIME		= res  : res == -1;	
		GetNum( "ARENA_MESSAGE_OPEN_INTERVAL"		, res ) ? config.ARENA_MESSAGE_OPEN_INTERVAL		= res  : res == -1;	
		GetNum( "ARENA_1X1_TIME_START_1"	, res ) ? config.ARENA_1X1_TIME_START_1		= res  : res == -1;
		GetNum( "ARENA_1X1_TIME_END_1"		, res ) ? config.ARENA_1X1_TIME_END_1		= res  : res == -1;
		GetNum( "ARENA_1X1_TIME_START_2"	, res ) ? config.ARENA_1X1_TIME_START_2		= res  : res == -1;
		GetNum( "ARENA_1X1_TIME_END_2"		, res ) ? config.ARENA_1X1_TIME_END_2		= res  : res == -1;
		GetNum( "ARENA_1X1_TIME_START_3"	, res ) ? config.ARENA_1X1_TIME_START_3		= res  : res == -1;
		GetNum( "ARENA_1X1_TIME_END_3"		, res ) ? config.ARENA_1X1_TIME_END_3		= res  : res == -1;
		GetNum( "ARENA_3X3_TIME_START_1"	, res ) ? config.ARENA_3X3_TIME_START_1		= res  : res == -1;
		GetNum( "ARENA_3X3_TIME_END_1"		, res ) ? config.ARENA_3X3_TIME_END_1		= res  : res == -1;
		GetNum( "ARENA_3X3_TIME_START_2"	, res ) ? config.ARENA_3X3_TIME_START_2		= res  : res == -1;
		GetNum( "ARENA_3X3_TIME_END_2"		, res ) ? config.ARENA_3X3_TIME_END_2		= res  : res == -1;
		GetNum( "ARENA_3X3_TIME_START_3"	, res ) ? config.ARENA_3X3_TIME_START_3		= res  : res == -1;
		GetNum( "ARENA_3X3_TIME_END_3"		, res ) ? config.ARENA_3X3_TIME_END_3		= res  : res == -1;
		GetNum( "ARENA_6X6_TIME_START_1"	, res ) ? config.ARENA_6X6_TIME_START_1		= res  : res == -1;
		GetNum( "ARENA_6X6_TIME_END_1"		, res ) ? config.ARENA_6X6_TIME_END_1		= res  : res == -1;
		GetNum( "ARENA_6X6_TIME_START_2"	, res ) ? config.ARENA_6X6_TIME_START_2		= res  : res == -1;
		GetNum( "ARENA_6X6_TIME_END_2"		, res ) ? config.ARENA_6X6_TIME_END_2		= res  : res == -1;
		GetNum( "ARENA_6X6_TIME_START_3"	, res ) ? config.ARENA_6X6_TIME_START_3		= res  : res == -1;
		GetNum( "ARENA_6X6_TIME_END_3"		, res ) ? config.ARENA_6X6_TIME_END_3		= res  : res == -1;
		GetNum( "ARENA_RATE"			, res ) ? config.ARENA_RATE				= res  : res == -1;
		GetNum( "ARENA_RATE_TEAM"		, res ) ? config.ARENA_RATE_TEAM		= res  : res == -1;
		GetNum( "ARENA_MATCH_COOLDOWN"	, res ) ? config.ARENA_MATCH_COOLDOWN	= res  : res == -1;	
		GetNum( "dont_save_ip_user_id"	, res ) ? config.dont_save_ip_user_id	= res  : res == -1;
		GetNum( "ARENA_1X1_SUNDAY"	, res ) ? config.ARENA_1X1_SUNDAY	= res  : res == -1;
		GetNum( "ARENA_1X1_MONDAY"	, res ) ? config.ARENA_1X1_MONDAY	= res  : res == -1;
		GetNum( "ARENA_1X1_TUESDAY"	, res ) ? config.ARENA_1X1_TUESDAY	= res  : res == -1;
		GetNum( "ARENA_1X1_WEDNESDAY"	, res ) ? config.ARENA_1X1_WEDNESDAY	= res  : res == -1;
		GetNum( "ARENA_1X1_THURSDAY"	, res ) ? config.ARENA_1X1_THURSDAY	= res  : res == -1;
		GetNum( "ARENA_1X1_FRIDAY"	, res ) ? config.ARENA_1X1_FRIDAY	= res  : res == -1;
		GetNum( "ARENA_1X1_SATURDAY"	, res ) ? config.ARENA_1X1_SATURDAY	= res  : res == -1;
		GetNum( "ARENA_3X3_SUNDAY"	, res ) ? config.ARENA_3X3_SUNDAY	= res  : res == -1;
		GetNum( "ARENA_3X3_MONDAY"	, res ) ? config.ARENA_3X3_MONDAY	= res  : res == -1;
		GetNum( "ARENA_3X3_TUESDAY"	, res ) ? config.ARENA_3X3_TUESDAY	= res  : res == -1;
		GetNum( "ARENA_3X3_WEDNESDAY"	, res ) ? config.ARENA_3X3_WEDNESDAY	= res  : res == -1;
		GetNum( "ARENA_3X3_THURSDAY"	, res ) ? config.ARENA_3X3_THURSDAY	= res  : res == -1;
		GetNum( "ARENA_3X3_FRIDAY"	, res ) ? config.ARENA_3X3_FRIDAY	= res  : res == -1;
		GetNum( "ARENA_3X3_SATURDAY"	, res ) ? config.ARENA_3X3_SATURDAY	= res  : res == -1;
		GetNum( "ARENA_6X6_SUNDAY"	, res ) ? config.ARENA_6X6_SUNDAY	= res  : res == -1;
		GetNum( "ARENA_6X6_MONDAY"	, res ) ? config.ARENA_6X6_MONDAY	= res  : res == -1;
		GetNum( "ARENA_6X6_TUESDAY"	, res ) ? config.ARENA_6X6_TUESDAY	= res  : res == -1;
		GetNum( "ARENA_6X6_WEDNESDAY"	, res ) ? config.ARENA_6X6_WEDNESDAY	= res  : res == -1;
		GetNum( "ARENA_6X6_THURSDAY"	, res ) ? config.ARENA_6X6_THURSDAY	= res  : res == -1;
		GetNum( "ARENA_6X6_FRIDAY"	, res ) ? config.ARENA_6X6_FRIDAY	= res  : res == -1;
		GetNum( "ARENA_6X6_SATURDAY"	, res ) ? config.ARENA_6X6_SATURDAY	= res  : res == -1;
		GetNum( "arena_hwid_check_enable", res ) ? config.arena_hwid_check_enable = res : res == -1; 
	}

	void LuaManager::FunctionsExec()
	{
		config.INIT();
	}

	#define LUA_MUTEX_BEGIN pthread_mutex_lock(&lua_mutex); \
							try { 

	#define LUA_MUTEX_END	} \
							catch (...) { printf("LUA::PANIC::EXCEPTION: ERROR \n"); } \
							pthread_mutex_unlock(&lua_mutex);

	void LuaManager::Init()
	{
		tick = 0;
		FName = "script.lua";
		pthread_mutex_init(&lua_mutex,0);
		LUA_MUTEX_BEGIN
		usleep(64);
		reload_tm = GetFileTime(FName);
		L = luaL_newstate();
		luaL_openlibs(L);
		FunctionsRegister();
		FunctionsExec();
		luaL_dofile(L, FName);
		SetItem();
		LuaRef Event = getGlobal(L, "Init");
		if(!Event.isNil())
		Event();
		else 
		printf("LUA::Event: NULL!!! Init \n");
		GetItem();
		LUA_MUTEX_END
	}

	void LuaManager::Update()
	{
		time_t last_tm = GetFileTime(FName);
		if(reload_tm == last_tm) return;	
		reload_tm = last_tm;
		LUA_MUTEX_BEGIN
		usleep(64);
		lua_close(L);
		L = luaL_newstate();
		luaL_openlibs(L);
		FunctionsRegister();
		FunctionsExec();
		luaL_dofile(L, FName);
		SetItem();
		LuaRef Event = getGlobal(L, "Update");
		if(!Event.isNil())
		Event();
		else 
		printf("LUA::Event: NULL!!! Update \n");
		GetItem();
		LUA_MUTEX_END
	}

	void LuaManager::HeartBeat()
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "HeartBeat");
		if(!Event.isNil())
		Event(tick);
		else 
		printf("LUA::Event: NULL!!! HeartBeat \n");
		LUA_MUTEX_END
		if( !(tick++ & 30) )
		Update();
	}

	time_t LuaManager::BidBeginTime(time_t now)
	{
		time_t res = 327600;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "BidBeginTime");
		if(!Event.isNil())
		res = Event(now)[0];
		else 
		printf("LUA::Event: NULL!!! BidBeginTime \n");
		LUA_MUTEX_END
		return res;
	}

	time_t LuaManager::BidEndTime(time_t now)
	{
		time_t res = 414000;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "BidEndTime");
		if(!Event.isNil())
		res = Event(now)[0];
		else 
		printf("LUA::Event: NULL!!! BidEndTime \n");
		LUA_MUTEX_END
		return res;
	}

	time_t LuaManager::BattleTime(time_t now)
	{
		time_t res = 591000;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "BattleTime");
		if(!Event.isNil())
		res = Event(now)[0];
		else 
		printf("LUA::Event: NULL!!! BattleTime \n");
		LUA_MUTEX_END
		return res;
	}

	time_t LuaManager::RewardTime(time_t now)
	{
		time_t res = 475200;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "RewardTime");
		if(!Event.isNil())
		res = Event(now)[0];
		else 
		printf("LUA::Event: NULL!!! RewardTime \n");
		LUA_MUTEX_END
		return res;
	}

	time_t LuaManager::BattleInterval(size_t num)
	{
		time_t res = 180;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "BattleInterval");
		if(!Event.isNil())
		res = Event(num)[0];
		else 
		printf("LUA::Event: NULL!!! BattleInterval \n");
		LUA_MUTEX_END
		if(res < 180) res = 180;
		return res;
	}

	time_t LuaManager::CountryBattleStartTime()
	{
		time_t res = (20 * 3600 + 20 * 60);
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "CountryBattleStartTime");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! CountryBattleStartTime \n");
		LUA_MUTEX_END
		return res;
	}	

	time_t LuaManager::CountryBattleBonusTime()
	{
		time_t res = (22 * 3600 + 21 * 60);
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "CountryBattleBonusTime");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! CountryBattleBonusTime \n");
		LUA_MUTEX_END
		return res;
	}

	time_t LuaManager::CountryBattleClearTime()
	{
		time_t res = (23 * 3600 + 30 * 60);
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "CountryBattleClearTime");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! CountryBattleClearTime \n");
		LUA_MUTEX_END
		return res;
	}

	size_t LuaManager::CountryMaxCount()
	{
		size_t res = 4;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "CountryMaxCount");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! CountryMaxCount \n");
		LUA_MUTEX_END
		if(res < 2 || res > 4)
		res = 4;
		return res;
	}
	
	size_t LuaManager::CountryBattleBonus()
	{
		time_t res = 85000;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "CountryBattleBonus");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! CountryBattleBonus \n");
		LUA_MUTEX_END
		return res;
	}

	int LuaManager::CountryBattleMaxBonus()
	{
		// Retorna o valor j谩 carregado do script.lua
		return config.COUNTRY_MAX_BONUS;
	}

	int LuaManager::CountryBattleItem()
	{
		int res = 36343;
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "CountryBattleItem");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! CountryBattleItem \n");
		LUA_MUTEX_END
		return res;
	}

	// Custom Arena 180
	time_t LuaManager::ArenaStartTime()
	{
		time_t res = (18 * 3600 + 20 * 60);
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "ArenaStartTime");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! ArenaStartTime \n");
		LUA_MUTEX_END
		return res;
	}	

	time_t LuaManager::ArenaEndTime()
	{
		time_t res = (22 * 3600 + 21 * 60);
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "ArenaEndTime");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! ArenaEndTime \n");
		LUA_MUTEX_END
		return res;
	}

	time_t LuaManager::ArenaClearTime()
	{
		time_t res = (23 * 3600 + 30 * 60);
		LUA_MUTEX_BEGIN
		usleep(8);
		LuaRef Event = getGlobal(L, "ArenaClearTime");
		if(!Event.isNil())
		res = Event()[0];
		else 
		printf("LUA::Event: NULL!!! ArenaClearTime \n");
		LUA_MUTEX_END
		return res;
	}

	void LuaManager::ArenaPlayerMessage(int roleid, int result)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "ArenaPlayerMessage");
		if(!Event.isNil())
		Event(roleid, result);
		else 
		printf("LUA::Event: NULL!!! ArenaPlayerMessage \n");
		LUA_MUTEX_END
	}
	
	void LuaManager::ArenaTimerCounter(int roleid, int tick)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "ArenaTimerCounter");
		if(!Event.isNil())
		Event(roleid, tick);
		else 
		printf("LUA::Event: NULL!!! ArenaTimerCounter \n");
		LUA_MUTEX_END
	}

	void LuaManager::ArenaTeamTimerCounter(int roleid, int tick)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "ArenaTeamTimerCounter");
		if(!Event.isNil())
		Event(roleid, tick);
		else 
		printf("LUA::Event: NULL!!! ArenaTeamTimerCounter \n");
		LUA_MUTEX_END
	}
	
	void LuaManager::ArenaTimerCooldown(int roleid, int tick)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "ArenaTimerCooldown");
		if(!Event.isNil())
		Event(roleid, tick);
		else 
		printf("LUA::Event: NULL!!! ArenaTimerCooldown \n");
		LUA_MUTEX_END
	}	

	void LuaManager::sendArena1x1EndDiscordWebHook(int id_red, int id_blue, int result, int tick, int damage_red, int damage_blue)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "sendArena1x1EndDiscordWebHook");
		if(!Event.isNil())
		Event(id_red, id_blue, result, tick, damage_red, damage_blue);
		else 
		printf("LUA::Event: NULL!!! sendArena1x1EndDiscordWebHook \n");
		LUA_MUTEX_END
	}

	void LuaManager::sendArena3x3EndDiscordWebHook(int id_red_1, int id_red_2, int id_red_3, int id_blue_1, int id_blue_2, int id_blue_3, int result, int tick)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "sendArena3x3EndDiscordWebHook");
		if(!Event.isNil())
		Event(id_red_1, id_red_2, id_red_3, id_blue_1, id_blue_2, id_blue_3, result, tick);
		else 
		printf("LUA::Event: NULL!!! sendArena3x3EndDiscordWebHook \n");
		LUA_MUTEX_END
	}

	void LuaManager::sendArena6x6EndDiscordWebHook(int id_red_1, int id_red_2, int id_red_3, int id_red_4, int id_red_5, int id_red_6, int id_blue_1, int id_blue_2, int id_blue_3, int id_blue_4, int id_blue_5, int id_blue_6, int result, int tick)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "sendArena6x6EndDiscordWebHook");
		if(!Event.isNil())
		Event(id_red_1, id_red_2, id_red_3, id_red_4, id_red_5, id_red_6, id_blue_1, id_blue_2, id_blue_3, id_blue_4, id_blue_5, id_blue_6, result, tick);
		else 
		printf("LUA::Event: NULL!!! sendArena6x6EndDiscordWebHook \n");
		LUA_MUTEX_END
	}
	
	void LuaManager::ArenaGetRoleName(int roleid, Octets& name)
	{
		LUA_MUTEX_BEGIN
		usleep(4);
		LuaRef Event = getGlobal(L, "ArenaGetRoleName");
		if (!Event.isNil())
		{
			const char* utf8_msg = Event(roleid)[0];
			size_t utf8_size = strlen(utf8_msg);
			if (utf8_size > 1 && utf8_size < 40)
			{
				name.clear();
				name.resize(utf8_size * 2 + 2);
				memset(name.begin(), 0x00, name.size());

				const utf8_t* utf8_msg_cast = reinterpret_cast<const utf8_t*>(utf8_msg);
				
				utf16_t* name_begin_cast = reinterpret_cast<utf16_t*>(name.begin());

				size_t utf16_size = name.size() / sizeof(utf16_t);

				utf8_to_utf16(utf8_msg_cast, utf8_size, name_begin_cast, utf16_size);
			}
		}
		else
		{
			printf("LUA::Event: NULL!!! ArenaGetRoleName \n");
		}
		LUA_MUTEX_END
	}

	int LuaManager::EventOnEnterServer(int roleid, Octets & hwid, int & gameid)
	{
		int res = 0;
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRole(roleid);
		if (pinfo && pinfo->gameid > 0)
		{
			pinfo->hwid = hwid;
			
			gameid = pinfo->gameid;
			if ( gameid > 1 && hwid.size() == 8 && *(unsigned long long*)hwid.begin() )
			{
				size_t counter = config.IS_TRUE_TAG_LIMITER(gameid);
				if (counter)
				{
					size_t count_users = UserContainer::GetInstance().GetTagHwidCounter( gameid, hwid );
					printf("LuaManager::EventOnEnterServer: roleid=%d, hwid=%lld, gameid=%d, counter=%lld , max_counter=%lld \n", roleid, *(unsigned long long*)hwid.begin(), gameid, count_users, counter );
					
					if ( count_users > counter)
					{
						res = 1;
						printf("LuaManager::EventOnEnterServer: roleid=%d , counter=%d, max_counter=%d res=%d \n", roleid, count_users, counter, res);
					}
				}
			}
		}
		//printf("LuaManager::EventOnEnterServer: roleid=%d , hSize = %d, gameid=%d, res = %d \n", roleid, hwid.size(), gameid, res);
		return res;
	}

	int LuaManager::EventOnSwitchServer(int roleid, int gameid)
	{
		int res = 0;
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRole(roleid);
		if (pinfo && pinfo->gameid > 0)
		{
			Octets hwid = pinfo->hwid;
			if ( gameid > 1 && hwid.size() == 8 && *(unsigned long long*)hwid.begin() )
			{
				size_t counter = config.IS_TRUE_TAG_LIMITER(gameid);
				if (counter)
				{
					size_t count_users = UserContainer::GetInstance().GetTagHwidCounter( gameid, hwid );
					printf("LuaManager::EventOnSwitchServer: roleid=%d, hwid=%lld, gameid=%d, counter=%lld , max_counter=%lld \n", roleid, *(unsigned long long*)hwid.begin(), gameid, count_users, counter );
					
					if ( count_users >= counter)
					{
						res = 1;
						printf("LuaManager::EventOnSwitchServer: roleid=%d , counter=%d, max_counter=%d res=%d \n", roleid, count_users, counter, res);
					}
				}
			}
		}
		//printf("LuaManager::EventOnSwitchServer: roleid=%d , gameid = %d res=%d \n", roleid, gameid, res);
		return res;
	}

	int LuaManager::EventOnPlayerLuaInfo(int roleid, Octets & info)
	{
		int res = 0;
		PlayerInfo * pinfo = UserContainer::GetInstance().FindRole(roleid);
		if (pinfo && pinfo->gameid > 0)
		{
			GDeliveryServer::GetInstance()->Send(pinfo->linksid, PlayerLuaInfo(roleid , pinfo->gameid, pinfo->localsid));
			res = 1;
			//TODO
		}
		//printf("LuaManager::EventOnPlayerLuaInfo: roleid=%d , size=%d, game_id=%d, res=%d \n", roleid, info.size(), pinfo->gameid, res);
		return res;
	}
	
	void LuaTimer::UpdateTimer()
	{
		LuaManager::GetInstance()->HeartBeat();
	}

	void LuaTimer::Run()
	{
		UpdateTimer();
		Thread::HouseKeeper::AddTimerTask(this,update_time);
	}

};

