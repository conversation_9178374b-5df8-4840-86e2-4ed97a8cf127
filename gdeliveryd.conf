[LogclientClient]
type            =   udp
port            =   11100
;address            =   **************
address         =   ************
so_sndbuf       =   ********
so_rcvbuf       =   16384
accumulate      =   ********

[LogclientTcpClient]
type            =   tcp
port            =   11101
;address            =   **************
address         =   ************
so_sndbuf       =   ********
so_rcvbuf       =   16384
accumulate      =   ********

[Intervals]
;set accounting interval,unit is second
account_interval	=	600
;set delrole interval,unit is second
delrole_interval	=	60
;set check forbidlogin user's map interval,unit is second
checkforbidlogin_interval = 60

[GDeliveryServer]
zoneid			=	1
aid				=	2
zonename		=	zone1
max_player_num	=	5000

type			=	tcp
port			=	9101
address			=	0.0.0.0
so_sndbuf		=	16384
so_rcvbuf		=	16384
tcp_nodelay		=	0
listen_backlog	=	10
accumulate		=	1048576
mtrace			=	/tmp/m_trace.link

[GAntiCheatClient]
type			=	tcp
port			=	29700
address			=	************
so_sndbuf		=	1638400
so_rcvbuf		=	1638400
ibuffermax		=	1638400
obuffermax		=	1638400
tcp_nodelay		=	0
accumulate		=	*********

[GAuthClient]
type			=	tcp
port			=	9200
address			=	127.0.0.1
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast	=	1
tcp_nodelay		=	0
accumulate		=	*********
isec			=	2
iseckey			=	123
osec			=	2
oseckey			=	456
shared_key		=	123456

[GProviderServer]
id				=	0
type			=	tcp
port			=	9301
address			=	0.0.0.0
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast		=	1
tcp_nodelay		=	0
accumulate		=	*********

[GameDBClient]
type			=	tcp
port			=	9402
address			=	127.0.0.1
so_sndbuf		=	16384
so_rcvbuf		=	16384
ibuffermax		=	16384
obuffermax		=	16384
;so_broadcast		=	1
tcp_nodelay		=	0
accumulate		=	*********
;isec			=	2
;iseckey			=	123
;osec			=	2
;oseckey			=	456

[GFactionClient]
type			=	tcp
port			=	9500
address			=	127.0.0.1
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast		=	1
tcp_nodelay		=	0
accumulate		=	*********
;isec			=	2
;iseckey			=	123
;osec			=	2
;oseckey			=	456

[GRoleDBClient]
type			=	tcp
port			=	15000
address			=	************
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast	=	1
tcp_nodelay		=	0
accumulate		=	*********
;isec			=	2
;iseckey			=	123
;osec			=	2
;oseckey			=	456

[UniqueNameClient]
type                    =       tcp
port                    =       29401
address                 =       127.0.0.1
so_sndbuf               =       16384
so_rcvbuf               =       16384
;so_broadcast           =       1
tcp_nodelay             =       0
accumulate              =       *********

[BattleBonus]
id						=		258751
countoflevel1			=		1
countoflevel2			=		1
countoflevel3			=		1
maxcount				=		10
proctype				=		0
specialid				=		258835
countofspecial			=		1
specialproctype			=		0
maxcountofspecial		=		10

[ThreadPool]
threads				=	(1,3)(100,1)(101,1)(0,1)
max_queuesize		=	1048576
prior_strict		=	1
