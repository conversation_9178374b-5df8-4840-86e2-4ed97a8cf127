
#ifndef __GNET_CLEARSTOREHOUSEPASSWD_HPP
#define __GNET_CLEARSTOREHOUSEPASSWD_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "clearstorehousepasswdarg"


namespace GNET
{

class ClearStorehousePasswd : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "clearstorehousepasswd"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// ClearStorehousePasswdArg *arg = (ClearStorehousePasswdArg *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// ClearStorehousePasswdArg *arg = (ClearStorehousePasswdArg *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
