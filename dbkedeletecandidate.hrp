
#ifndef __GNET_DBKEDELETECANDIDATE_HPP
#define __GNET_DBKEDELETECANDIDATE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbkedeletecandidatearg"
#include "dbkedeletecandidateres"

#include "kingelection.h"
#include "gamedbclient.hpp"

namespace GNET
{

class DBKEDeleteCandidate : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbkedeletecandidate"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBKEDeleteCandidateArg *arg = (DBKEDeleteCandidateArg *)argument;
		// DBKEDeleteCandidateRes *res = (DBKEDeleteCandidateRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		//DBKEDeleteCandidateArg *arg = (DBKEDeleteCandidateArg *)argument;
		DBKEDeleteCandidateRes *res = (DBKEDeleteCandidateRes *)result;
		DEBUG_PRINT("dbkedeletecandidate: rpc return. retcode=%d", res->retcode);
	}

	void OnTimeout()
	{
		// TODO Client Only
		DBKEDeleteCandidateArg *arg = (DBKEDeleteCandidateArg *)argument;
		Log::log(LOG_ERR,"dbkedeletecandidate: timeout.\n");
		GameDBClient::GetInstance()->SendProtocol( Rpc::Call(RPC_DBKEDELETECANDIDATE,arg) );
	}

};

};
#endif
