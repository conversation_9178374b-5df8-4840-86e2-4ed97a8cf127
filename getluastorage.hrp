
#ifndef __GNET_GETLUASTORAGE_HPP
#define __GNET_GETLUASTORAGE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_DB
#include "dbbuffer.h"
#endif
#include "getluastoragearg"
#include "getluastorageres"

namespace GNET
{

class GetLuaStorage : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getluastorage"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_DB
		GetLuaStorageArg *arg = (GetLuaStorageArg *)argument;
		GetLuaStorageRes *res = (GetLuaStorageRes *)result;
		Marshal::OctetsStream key, value;
		key << *arg;
		res->retcode = DBBuffer::buf_find( "base", key, value );
		if( 0 == res->retcode )
			value >> res->value;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetLuaStorageArg *arg = (GetLuaStorageArg *)argument;
		// GetLuaStorageRes *res = (GetLuaStorageRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
