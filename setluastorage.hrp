
#ifndef __GNET_SETLUASTORAGE_HPP
#define __GNET_SETLUASTORAGE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_DB
#include "dbbuffer.h"
#endif
#include "setluastoragearg"
#include "setluastorageres"

namespace GNET
{

class SetLuaStorage : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "setluastorage"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_DB
		SetLuaStorageArg *arg = (SetLuaStorageArg *)argument;
		SetLuaStorageRes *res = (SetLuaStorageRes *)result;
		Marshal::OctetsStream key, value;
		key << arg->key;
		value << arg->value;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// SetLuaStorageArg *arg = (SetLuaStorageArg *)argument;
		// SetLuaStorageRes *res = (SetLuaStorageRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
