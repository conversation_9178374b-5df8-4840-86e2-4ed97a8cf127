
#ifndef __GNET_SETNEWTRASHBOX_HPP
#define __GNET_SETNEWTRASHBOX_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_DB
#include "dbbuffer.h"
#endif
#include "setnewtrashboxarg"
#include "setnewtrashboxres"

namespace GNET
{

class SetNewTrashBox : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "setnewtrashbox"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_DB
		SetNewTrashBoxArg *arg = (SetNewTrashBoxArg *)argument;
		SetNewTrashBoxRes *res = (SetNewTrashBoxRes *)result;
		Marshal::OctetsStream key, value;
		key << arg->key;
		value << arg->value;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// SetNewTrashBoxArg *arg = (SetNewTrashBoxArg *)argument;
		// SetNewTrashBoxRes *res = (SetNewTrashBoxRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
