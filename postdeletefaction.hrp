
#ifndef __GNET_POSTDELETEFACTION_HPP
#define __GNET_POSTDELETEFACTION_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "postdeletefactionarg"
#include "postdeletefactionres"

namespace GNET
{

class PostDeleteFaction : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "postdeletefaction"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_BDB
		PostDeleteFactionArg *arg = (PostDeleteFactionArg *)argument;
		PostDeleteFactionRes *res = (PostDeleteFactionRes *)result;
		Marshal::OctetsStream key, value;
		key << arg->factionname;
		value << arg->factionid;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// PostDeleteFactionArg *arg = (PostDeleteFactionArg *)argument;
		// PostDeleteFactionRes *res = (PostDeleteFactionRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
