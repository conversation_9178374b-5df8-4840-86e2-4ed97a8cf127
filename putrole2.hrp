
#ifndef __GNET_PUTROLE2_HPP
#define __GNET_PUTROLE2_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "rolepair2"


namespace GNET
{

class PutRole2 : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "putrole2"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_BDB
		RolePair2 *arg = (RolePair2 *)argument;
		RpcRetcode *res = (RpcRetcode *)result;
		Marshal::OctetsStream key, value;
		key << arg->key;
		value << arg->value;
		res->retcode = DBBuffer::buf_insert( "base", key, value );
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// RolePair2 *arg = (RolePair2 *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
