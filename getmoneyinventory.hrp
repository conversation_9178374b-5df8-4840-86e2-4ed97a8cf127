
#ifndef __GNET_GETMONEYINVENTORY_HPP
#define __GNET_GETMONEYINVENTORY_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "getmoneyinventoryarg"
#include "getmoneyinventoryres"

namespace GNET
{

class GetMoneyInventory : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getmoneyinventory"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_BDB
		GetMoneyInventoryArg *arg = (GetMoneyInventoryArg *)argument;
		GetMoneyInventoryRes *res = (GetMoneyInventoryRes *)result;
		Marshal::OctetsStream key, value;
		key << *arg;
		res->retcode = DBBuffer::buf_find( "inventory", key, value );
		if( 0 == res->retcode )
			value >> res->goods;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetMoneyInventoryArg *arg = (GetMoneyInventoryArg *)argument;
		// GetMoneyInventoryRes *res = (GetMoneyInventoryRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
