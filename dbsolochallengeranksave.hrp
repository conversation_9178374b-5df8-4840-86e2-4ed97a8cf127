
#ifndef __GNET_DBSOLOCHALLENGERANKSAVE_HPP
#define __GNET_DBSOLOCHALLENGERANKSAVE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbsolochallengeranksavearg"
#include "dbsolochallengeranksaveres"

namespace GNET
{

class DBSoloChallengeRankSave : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbsolochallengeranksave"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBSoloChallengeRankSaveArg *arg = (DBSoloChallengeRankSaveArg *)argument;
		// DBSoloChallengeRankSaveRes *res = (DBSoloChallengeRankSaveRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// DBSoloChallengeRankSaveArg *arg = (DBSoloChallengeRankSaveArg *)argument;
		// DBSoloChallengeRankSaveRes *res = (DBSoloChallengeRankSaveRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
