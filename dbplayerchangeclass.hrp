
#ifndef __GNET_DBPLAYERCHANGECLASS_HPP
#define __GNET_DBPLAYERCHANGECLASS_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#include "dbplayerchangeclassarg"
#include "dbplayerchangeclassres"
#include "gamedbclient.hpp"

namespace GNET
{

class DBPlayerChangeClass : public ProxyRpc
{
#define	RPC_BASECLASS	ProxyRpc
	#include "dbplayerchangeclass"
#undef	RPC_BASECLASS

  void SyncGameServer(int roleid, int gsid, const GMailSyncData& syncdata, int retcode)
    {
        GProviderServer::GetInstance()->DispatchProtocol(gsid,
            GMailEndSync(0/*tid, must be 0*/, retcode, roleid, syncdata));
    }

	bool Delivery(Manager::Session::ID proxy_sid, const OctetsStream& osArg)
	{
		DBPlayerChangeClassArg arg;
		osArg >> arg;

		LOG_TRACE("DBPlayerChangeClass, roleid=%d, item_id=%d, item_num=%d, item_pos=%d", arg.roleid, arg.item_id, arg.item_num, arg.item_pos);

        PlayerInfo* pInfo = UserContainer::GetInstance().FindRoleOnline(arg.roleid);
        if (pInfo == NULL)
            return false;

		GMailSyncData sync(arg.syncdata);
		GDeliveryServer* dsm = GDeliveryServer::GetInstance();

		if (dsm->IsCentralDS())
		{
			sync.inventory.items.clear();
			SyncGameServer(arg.roleid, pInfo->gameid, sync, ERR_PR_OUTOFSERVICE);
			return false;
		}

		if (PlayerProfileMan::GetInstance()->HasMatchOptionMask(arg.roleid))
        {
            SetResult(DBPlayerChangeClassRes(ERR_PR_PROFILE));
            SendToSponsor();

            sync.inventory.items.clear();
            SyncGameServer(arg.roleid, pInfo->gameid, sync, ERR_PR_PROFILE);
            return false;
        }

		if( GameDBClient::GetInstance()->SendProtocol( *this ) )
		{
			return true;
		}
		else
		{
			SetResult(DBPlayerChangeClassRes(ERR_DELIVER_SEND));
			SendToSponsor();
			SyncGameServer(arg.roleid, pInfo->gameid, sync, ERR_DELIVER_SEND);
			return false;
		}
	}

	void PostProcess(Manager::Session::ID proxy_sid,const OctetsStream& osArg, const OctetsStream& osRes)
	{
		// TODO
		 DBPlayerChangeClassArg arg;
		 osArg >> arg;
		 DBPlayerChangeClassRes res;
		 osRes >> res;
		
		PlayerInfo* pinfo = UserContainer::GetInstance().FindRoleOnline(arg.roleid);
        if (pinfo != NULL)
        {
            SyncGameServer(arg.roleid, pinfo->gameid, res.syncdata, res.retcode);
        }

        if (res.retcode == ERR_SUCCESS)
        {
            GDeliveryServer* dsm = GDeliveryServer::GetInstance();
            dsm->rbcache.Lock();

            GRoleBase* grb = dsm->rbcache.GetDirectly(arg.roleid);
            if (grb != NULL)
            {
				unsigned char racetable[] = { 0, 0, 0, 1, 2, 3, 4, 5, 0, 0, 0, 0, 2, 3, 2, 0};

                grb->gender = arg.newgender;
				grb->cls = arg.newcls;
				grb->race = (arg.newcls < 16) ? racetable[arg.newcls] : 0;
                grb->custom_data = res.custom_data;
            }
            dsm->rbcache.UnLock();
        }
	}

	void OnTimeout( )
	{
		// TODO Client Only
	}

};

};
#endif
