
#ifndef __GNET_DBREFUPDATEREFERRER_HPP
#define __GNET_DBREFUPDATEREFERRER_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbrefupdatereferrerarg"
#include "referencemanager.h"


namespace GNET
{

class DBRefUpdateReferrer : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbrefupdatereferrer"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBRefUpdateReferrerArg *arg = (DBRefUpdateReferrerArg *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		DBRefUpdateReferrerArg *arg = (DBRefUpdateReferrerArg *)argument;
		RpcRetcode *res = (RpcRetcode *)result;

		if (res->retcode == ERR_SUCCESS)
			ReferenceManager::GetInstance()->OnDBUpdateReferrer(arg->referrer.userid);
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
