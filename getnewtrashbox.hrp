
#ifndef __GNET_GETNEWTRASHBOX_HPP
#define __GNET_GETNEWTRASHBOX_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_DB
#include "dbbuffer.h"
#endif
#include "getnewtrashboxarg"
#include "getnewtrashboxres"

namespace GNET
{

class GetNewTrashBox : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getnewtrashbox"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_DB
		GetNewTrashBoxArg *arg = (GetNewTrashBoxArg *)argument;
		GetNewTrashBoxRes *res = (GetNewTrashBoxRes *)result;
		Marshal::OctetsStream key, value;
		key << *arg;
		res->retcode = DBBuffer::buf_find( "base", key, value );
		if( 0 == res->retcode )
			value >> res->value;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetNewTrashBoxArg *arg = (GetNewTrashBoxArg *)argument;
		// GetNewTrashBoxRes *res = (GetNewTrashBoxRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
