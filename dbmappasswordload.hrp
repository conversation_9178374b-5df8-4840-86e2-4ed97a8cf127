
#ifndef __GNET_DBMAPPASSWORDLOAD_HPP
#define __GNET_DBMAPPASSWORDLOAD_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbmappasswordloadarg"
#include "dbmappasswordloadres"

#include "mappasswd.h"
#include "gamedbclient.hpp"

namespace GNET
{

class DBMapPasswordLoad : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbmappasswordload"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBMapPasswordLoadArg *arg = (DBMapPasswordLoadArg *)argument;
		// DBMapPasswordLoadRes *res = (DBMapPasswordLoadRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		DBMapPasswordLoadArg *arg = (DBMapPasswordLoadArg *)argument;
		DBMapPasswordLoadRes *res = (DBMapPasswordLoadRes *)result;

        Log::log(LOG_INFO, "DBMapPasswordLoad: RPC return. res->retcode=%d, res->data.size()=%d, res->handle.size()=%d\n", res->retcode, res->data.size(), res->handle.size());

        if (res->retcode == ERR_SUCCESS)
        {
            bool finish = ((res->data.size() == 0) || (res->handle.size() == 0));
            Passwd::GetInstance().OnDBLoad(res->data, finish);

            if (res->handle.size() != 0)
            {
                GameDBClient::GetInstance()->SendProtocol(Rpc::Call(RPC_DBMAPPASSWORDLOAD, DBMapPasswordLoadArg(res->handle)));
            }
        }
        else if (res->retcode == ERR_AGAIN)
        {
            GameDBClient::GetInstance()->SendProtocol(Rpc::Call(RPC_DBMAPPASSWORDLOAD, arg));
        }
	}

	void OnTimeout()
	{
        Log::log(LOG_WARNING, "DBMapPasswordLoad: RPC timeout. Resend request.\n");
        DBMapPasswordLoadArg* arg = (DBMapPasswordLoadArg*)argument;
        GameDBClient::GetInstance()->SendProtocol(Rpc::Call(RPC_DBMAPPASSWORDLOAD, arg));
	}

};

};
#endif
