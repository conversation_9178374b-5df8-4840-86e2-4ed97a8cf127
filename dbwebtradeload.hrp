
#ifndef __GNET_DBWEBTRADELOAD_HPP
#define __GNET_DBWEBTRADELOAD_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbwebtradeloadarg"
#include "dbwebtradeloadres"

#include "gamedbclient.hpp"
#include "webtrademarket.h"

namespace GNET
{

class DBWebTradeLoad : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbwebtradeload"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBWebTradeLoadArg *arg = (DBWebTradeLoadArg *)argument;
		// DBWebTradeLoadRes *res = (DBWebTradeLoadRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		DBWebTradeLoadArg *arg = (DBWebTradeLoadArg *)argument;
		DBWebTradeLoadRes *res = (DBWebTradeLoadRes *)result;
		DEBUG_PRINT("dbwebtradeload: received. res->max_sn=%lld,res->items.size()=%d,res->handle.size()=%d retcode=%d\n",
				res->max_sn,res->items.size(),res->handle.size(), res->retcode);
		if(res->retcode == ERR_SUCCESS)
		{
			WebTradeMarket::GetInstance().OnDBLoad(res->max_sn,res->items,res->items.size()==0||res->handle.size()==0);	
			if(res->handle.size()!=0)
				GameDBClient::GetInstance()->SendProtocol( Rpc::Call(RPC_DBWEBTRADELOAD,DBWebTradeLoadArg(res->handle)) );		
		}
		else if(res->retcode==ERR_AGAIN)
		{
			GameDBClient::GetInstance()->SendProtocol( Rpc::Call(RPC_DBWEBTRADELOAD,arg) );		
		}
		
	}

	void OnTimeout()
	{
		// TODO Client Only
		DBWebTradeLoadArg * arg = (DBWebTradeLoadArg *)argument;
		Log::log(LOG_WARNING,"dbwebtradeload: rpc timeout. Resend request.\n");
		GameDBClient::GetInstance()->SendProtocol( Rpc::Call(RPC_DBWEBTRADELOAD,arg) ); 
	}

};

};
#endif
