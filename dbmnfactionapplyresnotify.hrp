
#ifndef __GNET_DBMNFACTIONAPPLYRESNOTIFY_HPP
#define __GNET_DBMNFACTIONAPPLYRESNOTIFY_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbmnfactionapplyresnotifyarg"
#include "dbmnfactionapplyresnotifyres"

#include "cdcmnfbattleman.h"

namespace GNET
{

class DBMNFactionApplyResNotify : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbmnfactionapplyresnotify"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBMNFactionApplyResNotifyArg *arg = (DBMNFactionApplyResNotifyArg *)argument;
		// DBMNFactionApplyResNotifyRes *res = (DBMNFactionApplyResNotifyRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		DEBUG_PRINT("file=%s\tfunc=%s\tline=%d\n", __FILE__, __FUNCTION__, __LINE__);
		DBMNFactionApplyResNotifyArg *arg = (DBMNFactionApplyResNotifyArg *)argument;
		DBMNFactionApplyResNotifyRes *res = (DBMNFactionApplyResNotifyRes *)result;

		if(res->retcode == ERR_SUCCESS)
		{
			CDC_MNFactionBattleMan::GetInstance()->OnNotifyDBApplyResSuccess(arg->status, arg->rejected_list);
		}
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
