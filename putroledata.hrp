
#ifndef __GNET_PUTROLEDATA_HPP
#define __GNET_PUTROLEDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "roledatapair"


namespace GNET
{

class PutRoleData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "putroledata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// RoleDataPair *arg = (RoleDataPair *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// RoleDataPair *arg = (RoleDataPair *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
