
#ifndef __GNET_GETLOTTERYDATA_HPP
#define __GNET_GETLOTTERYDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "getlotterydataarg"
#include "getlotterydatares"

namespace GNET
{

class GetLotteryData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getlotterydata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// GetLotteryDataArg *arg = (GetLotteryDataArg *)argument;
		// GetLotteryDataRes *res = (GetLotteryDataRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetLotteryDataArg *arg = (GetLotteryDataArg *)argument;
		// GetLotteryDataRes *res = (GetLotteryDataRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
