
#ifndef __GNET_GETROLEPOCKET_HPP
#define __GNET_GETROLEPOCKET_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "roleid"
#include "rolepocketres"

namespace GNET
{

class GetRolePocket : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getrolepocket"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// RoleId *arg = (RoleId *)argument;
		// RolePocketRes *res = (RolePocketRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// RoleId *arg = (RoleId *)argument;
		// RolePocketRes *res = (RolePocketRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
