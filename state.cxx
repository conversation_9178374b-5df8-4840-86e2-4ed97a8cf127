#include "callid.hxx"

#ifdef WIN32
#include <winsock2.h>
#include "gnproto.h"
#include "gncompress.h"
#else
#include "protocol.h"
#include "binder.h"
#endif

namespace GNET
{

static GNET::Protocol::Type _state_GDeliverServer[] = 
{
	PROTOCOL_PLAYERLOGIN,
	PROTOCOL_PLAYERSTATUSSYNC,
	PROTOCOL_ENTERWORLD,
	PROTOCOL_STATUSANNOUNCE,
	PROTOCOL_ROLELIST,
	PROTOCOL_CREATEROLE,
	PROTOCOL_<PERSON>LETEROLE,
	PROTOCOL_UNDODELETEROLE,
	PROTOCOL_PLAYERBASEINFO,
	PROTOCOL_<PERSON>LAYERBASEINFOCRC,
	PROTOCOL_GETPLAYERIDBYNAME,
	PROTOCOL_SETCUSTOMDATA,
	PROTOCOL_GETCUSTOMDATA,
	PROTOCOL_SETUICONFIG,
	PROTOCOL_GETUICONFIG,
	PROTOCOL_SETHELPSTATES,
	PROTOCOL_GETHELPSTATES,
	PROTOCOL_GETPLAYERBRIE<PERSON>NFO,
	PROTOCOL_GMGETPLAYERCONSUMEINFO,
	PROTOCOL_COLLECTCLIENTMACHINEINFO,
	PROTOCOL_CANC<PERSON>WAITQUEUE,
	PROTOCOL_PUBLICCHAT,
	PROTOCOL_PRIVATECHAT,
	PROTOCOL_ARENABATTLERESULT,
	PROTOCOL_ARENABATTLEEND,
	PROTOCOL_ARENABATTLESTART,
	PROTOCOL_ARENABATTLEJOIN,
	PROTOCOL_ARENABATTLEQUIT,
	PROTOCOL_ARENABATTLEFAILED,
	PROTOCOL_HWIDPLAYERDATA,
	PROTOCOL_PLAYERTELEPORT,
	PROTOCOL_PLAYERLUAINFO,
	PROTOCOL_ADDFRIEND,
	PROTOCOL_ADDFRIENDREMARKS,
	PROTOCOL_GETFRIENDS,
	PROTOCOL_GETENEMYLIST,
	PROTOCOL_SETGROUPNAME,
	PROTOCOL_SETFRIENDGROUP,
	PROTOCOL_DELFRIEND,
	PROTOCOL_DELFRIEND_RE,
	PROTOCOL_FRIENDSTATUS,
	RPC_ADDFRIENDRQST,
	PROTOCOL_GETSAVEDMSG,
	PROTOCOL_CHATROOMCREATE,
	PROTOCOL_CHATROOMINVITE,
	PROTOCOL_CHATROOMINVITE_RE,
	PROTOCOL_CHATROOMJOIN,
	PROTOCOL_CHATROOMLEAVE,
	PROTOCOL_CHATROOMEXPEL,
	PROTOCOL_CHATROOMSPEAK,
	PROTOCOL_CHATROOMLIST,
	PROTOCOL_SENDAUMAIL,
	PROTOCOL_PLAYERREQUITEFRIEND,
	PROTOCOL_GETSAVEDMSG2,
	PROTOCOL_TRADESTART,
	PROTOCOL_TRADEADDGOODS,
	PROTOCOL_TRADEREMOVEGOODS,
	PROTOCOL_TRADESUBMIT,
	PROTOCOL_TRADEMOVEOBJ,
	PROTOCOL_TRADECONFIRM,
	PROTOCOL_TRADEDISCARD,
	RPC_TRADESTARTRQST,
	RPC_USERLOGIN,
	RPC_USERLOGIN2,
	RPC_GQUERYPASSWD,
	PROTOCOL_SWITCHSERVERCANCEL,
	RPC_PLAYERPOSITIONRESETRQST,
	PROTOCOL_GMRESTARTSERVER,
	PROTOCOL_GMONLINENUM,
	PROTOCOL_GMLISTONLINEUSER,
	PROTOCOL_GMKICKOUTUSER,
	PROTOCOL_ACKICKOUTUSER,
	PROTOCOL_GMFORBIDSELLPOINT,
	PROTOCOL_GMKICKOUTROLE,
	PROTOCOL_GMSHUTUP,
	PROTOCOL_GMSHUTUPROLE,
	PROTOCOL_GMTOGGLECHAT,
	PROTOCOL_GMFORBIDROLE,
	PROTOCOL_GMPRIVILEGECHANGE,
	PROTOCOL_REPORT2GM,
	PROTOCOL_COMPLAIN2GM,
	PROTOCOL_ANNOUNCELINKTYPE,
	RPC_GMQUERYROLEINFO,
	PROTOCOL_SETMAXONLINENUM,
	RPC_GETMAXONLINENUM,
	RPC_GMGETGAMEATTRI,
	RPC_GMSETGAMEATTRI,
	PROTOCOL_GMCONTROLGAME,
	PROTOCOL_VERIFYMASTER,
	PROTOCOL_VERIFYMASTER_RE,
	PROTOCOL_GMSETTIMELESSAUTOLOCK,
	PROTOCOL_IWEBAUTOLOCKGET,
	PROTOCOL_IWEBAUTOLOCKSET,
	PROTOCOL_ACREPORT,
	PROTOCOL_ACANSWER,
	PROTOCOL_ACPROTOSTAT,
	PROTOCOL_REPORTIP,
	PROTOCOL_CHECKNEWMAIL,
	PROTOCOL_SYSSENDMAIL,
	PROTOCOL_SYSRECOVEREDOBJMAIL,
	RPC_DBSELLPOINT,
	RPC_DBBUYPOINT,
	PROTOCOL_GETSELLLIST,
	PROTOCOL_FINDSELLPOINTINFO,
	PROTOCOL_SELLCANCEL,
	PROTOCOL_BATTLEGETMAP,
	PROTOCOL_BATTLESTATUS,
	PROTOCOL_COUNTRYBATTLEMOVE,
	PROTOCOL_COUNTRYBATTLEGETMAP,
	PROTOCOL_COUNTRYBATTLEGETPLAYERLOCATION,
	PROTOCOL_COUNTRYBATTLEGETCONFIG,
	PROTOCOL_COUNTRYBATTLEGETSCORE,
	PROTOCOL_COUNTRYBATTLEPREENTER,
	PROTOCOL_COUNTRYBATTLERETURNCAPITAL,
	PROTOCOL_COUNTRYBATTLEKINGASSIGNASSAULT,
	PROTOCOL_COUNTRYBATTLEKINGRESETBATTLELIMIT,
	PROTOCOL_COUNTRYBATTLEGETBATTLELIMIT,
	PROTOCOL_COUNTRYBATTLEGETKINGCOMMANDPOINT,
	PROTOCOL_GETCNETSERVERCONFIG,
	PROTOCOL_CASHLOCK,
	PROTOCOL_CASHPASSWORDSET,
	RPC_MATRIXPASSWD,
	RPC_MATRIXPASSWD2,
	RPC_MATRIXTOKEN,
	PROTOCOL_MATRIXFAILURE,
	PROTOCOL_AUTOLOCKSET,
	RPC_QUERYUSERID,
	RPC_FORBIDUSER,
	PROTOCOL_DISABLEAUTOLOCK,
	PROTOCOL_REFWITHDRAWBONUS,
	PROTOCOL_REFLISTREFERRALS,
	PROTOCOL_REFGETREFERENCECODE,
	PROTOCOL_EXCHANGECONSUMEPOINTS,
	PROTOCOL_GETREWARDLIST,
	PROTOCOL_WEBTRADEROLEPREPOST,
	PROTOCOL_WEBTRADEROLEPRECANCELPOST,
	PROTOCOL_WEBTRADEROLEGETDETAIL,
	PROTOCOL_USERCOUPON,
	PROTOCOL_USERCOUPONEXCHANGE,
	PROTOCOL_USERADDCASH,
	PROTOCOL_SSOGETTICKET,
	PROTOCOL_QPGETACTIVATEDSERVICES,
	PROTOCOL_QPADDCASH,
	PROTOCOL_REPORTCHAT,
	PROTOCOL_PLAYERACCUSE,
	RPC_PLAYERIDENTITYMATCH,
	PROTOCOL_ANNOUNCELINKVERSION,
	RPC_CASHMONEYEXCHANGECONTROL,
	RPC_SERVERFORBIDCONTROL,
	PROTOCOL_PSHOPPLAYERGET,
	PROTOCOL_PSHOPLIST,
	PROTOCOL_PSHOPLISTITEM,
	PROTOCOL_PLAYERPROFILEGETPROFILEDATA,
	PROTOCOL_PLAYERPROFILESETPROFILEDATA,
	PROTOCOL_PLAYERPROFILEGETMATCHRESULT,
	PROTOCOL_TANKBATTLEPLAYERGETRANK,
	PROTOCOL_FACTIONRESOURCEBATTLEGETMAP,
	PROTOCOL_FACTIONRESOURCEBATTLEGETRECORD,
	PROTOCOL_MNGETPLAYERLASTENTERINFO,
	PROTOCOL_MNGETFACTIONBRIEFINFO,
	PROTOCOL_MNGETFACTIONINFO,
	PROTOCOL_LOTTERYTICKETFLOORINFOGET,
};

GNET::Protocol::Manager::Session::State state_GDeliverServer(_state_GDeliverServer,
						sizeof(_state_GDeliverServer)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GAuthClient[] = 
{
	PROTOCOL_KEYEXCHANGE,
	PROTOCOL_STATUSANNOUNCE,
	PROTOCOL_KICKOUTUSER,
	PROTOCOL_ACCOUNTINGRESPONSE,
	PROTOCOL_QUERYUSERPRIVILEGE_RE,
	PROTOCOL_QUERYUSERFORBID_RE,
	PROTOCOL_UPDATEREMAINTIME,
	PROTOCOL_TRANSBUYPOINT_RE,
	RPC_GQUERYPASSWD,
	RPC_USERLOGIN,
	RPC_USERLOGIN2,
	RPC_USERLOGOUT,
	RPC_CASHSERIAL,
	RPC_GETADDCASHSN,
	PROTOCOL_ADDCASH,
	PROTOCOL_ADDCASH_RE,
	PROTOCOL_GETPLAYERIDBYNAME,
	PROTOCOL_SYSSENDMAIL,
	PROTOCOL_SYSSENDMAIL3,
	PROTOCOL_VERIFYMASTER,
	PROTOCOL_VERIFYMASTER_RE,
	RPC_MATRIXPASSWD,
	RPC_MATRIXPASSWD2,
	RPC_MATRIXTOKEN,
	PROTOCOL_MATRIXFAILURE,
	PROTOCOL_ADDICTIONCONTROL,
	PROTOCOL_BILLINGREQUEST,
	PROTOCOL_BILLINGBALANCE,
	PROTOCOL_BILLINGBALANCESA_RE,
	PROTOCOL_BILLINGCONFIRM_RE,
	RPC_QUERYUSERID,
	PROTOCOL_NETBARANNOUNCE,
	PROTOCOL_AUTHDVERSION,
	RPC_GETUSERCOUPON,
	RPC_COUPONEXCHANGE,
	RPC_INSTANTADDCASH,
	PROTOCOL_SSOGETTICKETREP,
	PROTOCOL_AU2GAME,
	PROTOCOL_DISCOUNTANNOUNCE,
};

GNET::Protocol::Manager::Session::State state_GAuthClient(_state_GAuthClient,
						sizeof(_state_GAuthClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GProviderDeliveryServer[] = 
{
	PROTOCOL_ANNOUNCEPROVIDERID,
	PROTOCOL_PLAYERLOGIN_RE,
	PROTOCOL_PLAYERKICKOUT_RE,
	PROTOCOL_PLAYERLOGOUT,
	PROTOCOL_PLAYEROFFLINE_RE,
	PROTOCOL_QUERYPLAYERSTATUS,
	PROTOCOL_GETTASKDATA,
	PROTOCOL_SETTASKDATA,
	PROTOCOL_PLAYERINFOUPDATE,
	PROTOCOL_PLAYERTEAMOP,
	PROTOCOL_PLAYERTEAMMEMBEROP,
	PROTOCOL_GTRADESTART_RE,
	PROTOCOL_GTRADEDISCARD,
	PROTOCOL_KEEPALIVE,
	PROTOCOL_DISCONNECTPLAYER,
	PROTOCOL_SWITCHSERVERSTART,
	PROTOCOL_SWITCHSERVERCANCEL,
	PROTOCOL_SWITCHSERVERSUCCESS,
	PROTOCOL_SWITCHSERVERTIMEOUT,
	PROTOCOL_SETCHATEMOTION,
	PROTOCOL_FACEMODIFY,
	PROTOCOL_FACEMODIFYCANCEL,
	PROTOCOL_GMRESTARTSERVER_RE,
	PROTOCOL_GETMAILLIST,
	PROTOCOL_GETMAIL,
	PROTOCOL_GETMAILATTACHOBJ,
	PROTOCOL_DELETEMAIL,
	PROTOCOL_PRESERVEMAIL,
	PROTOCOL_PLAYERSENDMAIL,
	PROTOCOL_PLAYERSENDMASSMAIL,
	PROTOCOL_AUCTIONOPEN,
	PROTOCOL_AUCTIONBID,
	PROTOCOL_SENDAUCTIONBID,
	PROTOCOL_SENDBATTLECHALLENGE,
	PROTOCOL_AUCTIONLIST,
	PROTOCOL_AUCTIONCLOSE,
	PROTOCOL_AUCTIONGET,
	PROTOCOL_AUCTIONGETITEM,
	PROTOCOL_AUCTIONATTENDLIST,
	PROTOCOL_AUCTIONEXITBID,
	PROTOCOL_AUCTIONLISTUPDATE,
	PROTOCOL_QUERYREWARDTYPE,
	PROTOCOL_QUERYGAMESERVERATTR,
	RPC_DBSELLPOINT,
	RPC_DBBUYPOINT,
	PROTOCOL_GETSELLLIST,
	PROTOCOL_SELLCANCEL,
	PROTOCOL_GMCONTROLGAME_RE,
	PROTOCOL_ACREPORTCHEATER,
	PROTOCOL_ACTRIGGERQUESTION,
	PROTOCOL_BATTLECHALLENGE,
	PROTOCOL_BATTLECHALLENGEMAP,
	PROTOCOL_BATTLEENTER,
	PROTOCOL_BATTLESERVERREGISTER,
	PROTOCOL_BATTLESTART_RE,
	PROTOCOL_DEBUGCOMMAND,
	RPC_BATTLEEND,
	PROTOCOL_CHATBROADCAST,
	PROTOCOL_STOCKCOMMISSION,
	PROTOCOL_STOCKACCOUNT,
	PROTOCOL_STOCKTRANSACTION,
	PROTOCOL_STOCKBILL,
	PROTOCOL_STOCKCANCEL,
	RPC_PUTSPOUSE,
	PROTOCOL_BILLINGREQUEST,
	PROTOCOL_BILLINGBALANCE,
	PROTOCOL_BILLINGBALANCESA,
	PROTOCOL_BILLINGCONFIRM,
	PROTOCOL_BILLINGCANCEL,
	PROTOCOL_PUBLICCHAT,
	PROTOCOL_SENDREFCASHUSED,
	PROTOCOL_SENDTASKREWARD,
	PROTOCOL_REFGETREFERENCECODE_RE,
	PROTOCOL_WEBTRADEPREPOST,
	PROTOCOL_WEBTRADEPRECANCELPOST,
	PROTOCOL_WEBTRADELIST,
	PROTOCOL_WEBTRADEGETITEM,
	PROTOCOL_WEBTRADEATTENDLIST,
	PROTOCOL_WEBTRADEGETDETAIL,
	PROTOCOL_WEBTRADEUPDATE,
	PROTOCOL_PRIVATECHAT,
	PROTOCOL_ARENABATTLERESULT,
	PROTOCOL_ARENABATTLEEND,
	PROTOCOL_ARENABATTLESTART,
	PROTOCOL_ARENABATTLEJOIN,
	PROTOCOL_ARENABATTLEQUIT,
	PROTOCOL_ARENABATTLEFAILED,
	PROTOCOL_HWIDPLAYERDATA,
	PROTOCOL_PLAYERTELEPORT,
	PROTOCOL_PLAYERLUAINFO,
	PROTOCOL_SYSAUCTIONLIST,
	PROTOCOL_SYSAUCTIONGETITEM,
	PROTOCOL_SYSAUCTIONACCOUNT,
	PROTOCOL_SYSAUCTIONBID,
	PROTOCOL_SYSAUCTIONCASHTRANSFER,
	RPC_SYSAUCTIONPREPAREITEM,
	RPC_GETFACTIONFORTRESS,
	RPC_PUTFACTIONFORTRESS,
	PROTOCOL_FACTIONSERVERREGISTER,
	PROTOCOL_NOTIFYFACTIONFORTRESSSTATE,
	PROTOCOL_FACTIONFORTRESSENTER,
	PROTOCOL_FACTIONFORTRESSLIST,
	PROTOCOL_FACTIONFORTRESSCHALLENGE,
	PROTOCOL_FACTIONFORTRESSBATTLELIST,
	PROTOCOL_FACTIONFORTRESSGET,
	PROTOCOL_SNSROLEBRIEFUPDATE,
	PROTOCOL_NOTIFYPLAYERJOINORLEAVEFORCE,
	PROTOCOL_INCREASEFORCEACTIVITY,
	PROTOCOL_SYNMUTADATA,
	PROTOCOL_COUNTRYBATTLEAPPLY,
	PROTOCOL_COUNTRYBATTLEJOINNOTICE,
	PROTOCOL_COUNTRYBATTLELEAVENOTICE,
	PROTOCOL_COUNTRYBATTLEONLINENOTICE,
	PROTOCOL_COUNTRYBATTLEOFFLINENOTICE,
	PROTOCOL_COUNTRYBATTLEENTERMAPNOTICE,
	PROTOCOL_COUNTRYBATTLESERVERREGISTER,
	PROTOCOL_COUNTRYBATTLESTART_RE,
	PROTOCOL_COUNTRYBATTLEEND,
	PROTOCOL_GCODEXREQUESTSTORAGE,
	PROTOCOL_GCODEXREQUESTSTORAGE_RE,
	PROTOCOL_SSOGETTICKET,
	PROTOCOL_PLAYERCHANGEDS_RE,
	PROTOCOL_TRYCHANGEDS,
	PROTOCOL_PLAYERRENAME,
	RPC_DBPLAYERCHANGEGENDER,
	PROTOCOL_UPDATESOLOCHALLENGERANK,
	PROTOCOL_GETSOLOCHALLENGERANK,
	PROTOCOL_UPDATEENEMYLIST,
	PROTOCOL_KEGETSTATUS,
	PROTOCOL_KECANDIDATEAPPLY,
	PROTOCOL_KEVOTING,
	PROTOCOL_PSHOPCREATE,
	PROTOCOL_PSHOPBUY,
	PROTOCOL_PSHOPSELL,
	PROTOCOL_PSHOPCANCELGOODS,
	PROTOCOL_PSHOPPLAYERBUY,
	PROTOCOL_PSHOPPLAYERSELL,
	PROTOCOL_PSHOPSETTYPE,
	PROTOCOL_PSHOPACTIVE,
	PROTOCOL_PSHOPMANAGEFUND,
	PROTOCOL_PSHOPDRAWITEM,
	PROTOCOL_PSHOPCLEARGOODS,
	PROTOCOL_PSHOPSELFGET,
	PROTOCOL_PLAYERGIVEPRESENT,
	PROTOCOL_PLAYERASKFORPRESENT,
	PROTOCOL_TOUCHPOINTQUERY,
	PROTOCOL_TOUCHPOINTCOST,
	PROTOCOL_GIFTCODEREDEEM,
	PROTOCOL_MOBILESERVERREGISTER,
	PROTOCOL_UNIQUEDATAMODIFYREQUIRE,
	PROTOCOL_TANKBATTLESERVERREGISTER,
	PROTOCOL_TANKBATTLEPLAYERAPPLY,
	PROTOCOL_TANKBATTLEPLAYERENTER,
	PROTOCOL_TANKBATTLEPLAYERLEAVE,
	PROTOCOL_TANKBATTLESTART_RE,
	PROTOCOL_TANKBATTLEEND,
	PROTOCOL_TANKBATTLEPLAYERSCOREUPDATE,
	PROTOCOL_AUTOTEAMCONFIGREGISTER,
	PROTOCOL_AUTOTEAMSETGOAL,
	PROTOCOL_AUTOTEAMPLAYERREADY_RE,
	PROTOCOL_FACTIONRESOURCEBATTLEREQUESTCONFIG_RE,
	PROTOCOL_FACTIONRESOURCEBATTLESERVERREGISTER,
	PROTOCOL_FACTIONRESOURCEBATTLEEVENTNOTICE,
	PROTOCOL_FACTIONRESOURCEBATTLEPLAYERQUERY,
	PROTOCOL_MNFACTIONBATTLEAPPLY,
	PROTOCOL_MNBATTLESERVERREGISTER,
	PROTOCOL_MNDOMAINBATTLESTART_RE,
	PROTOCOL_MNDOMAINBATTLEENTER,
	PROTOCOL_MNDOMAINBATTLEENTERSUCCESSNOTICE,
	PROTOCOL_MNDOMAINBATTLELEAVENOTICE,
	PROTOCOL_MNDOMAINBATTLEEND,
	PROTOCOL_MNGETTOPLIST,
	PROTOCOL_MNGETDOMAINDATA,
	PROTOCOL_GCODEXREQUESTSTORAGE,
	PROTOCOL_GCODEXREQUESTSTORAGE_RE,
};

GNET::Protocol::Manager::Session::State state_GProviderDeliveryServer(_state_GProviderDeliveryServer,
						sizeof(_state_GProviderDeliveryServer)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_GameDBClient[] = 
{
	PROTOCOL_DELROLEANNOUNCE,
	RPC_DBTRANSFERROLE,
	RPC_DBCREATEROLE,
	RPC_DBDELETEROLE,
	RPC_DBUNDODELETEROLE,
	RPC_PUTUSER,
	RPC_GETUSER,
	RPC_DELUSER,
	RPC_GETROLE,
	RPC_GETROLEINFO,
	RPC_DELROLE,
	RPC_DELROLE,
	RPC_PUTROLEBASE,
	RPC_GETROLEBASE,
	RPC_GETROLEPOCKET,
	RPC_PUTROLEPOCKET,
	RPC_PUTROLESTATUS,
	RPC_GETROLESTATUS,
	RPC_PUTROLEEQUIPMENT,
	RPC_GETROLEEQUIPMENT,
	RPC_PUTROLETASK,
	RPC_GETROLETASK,
	RPC_PUTROLEDATA,
	RPC_GETROLEDATA,
	RPC_TRADEINVENTORY,
	RPC_TRADESAVE,
	RPC_PUTROLE,
	RPC_GETMONEYINVENTORY,
	RPC_PUTMONEYINVENTORY,
	RPC_GETROLEBASESTATUS,
	RPC_PUTROLESTOREHOUSE,
	RPC_GETROLESTOREHOUSE,
	RPC_PUTROLEFORBID,
	RPC_GETROLEFORBID,
	RPC_GETROLEID,
	RPC_GETFRIENDLIST,
	RPC_PUTFRIENDLIST,
	RPC_PUTMESSAGE,
	RPC_GETMESSAGE,
	RPC_GETTASKDATARPC,
	RPC_PUTTASKDATARPC,
	RPC_DBVERIFYMASTER,
	RPC_DBGETCONSUMEINFOS,
	RPC_DBMAPPASSWORDLOAD,
	RPC_DBMAPPASSWORDSAVE,
	RPC_DBSOLOCHALLENGERANKLOAD,
	RPC_DBSOLOCHALLENGERANKSAVE,
	RPC_SETLUASTORAGE,
	RPC_GETLUASTORAGE,
	RPC_SETNEWTRASHBOX,
	RPC_GETNEWTRASHBOX,
	RPC_SETNEWROLEDETAIL,
	RPC_GETNEWROLEDETAIL,
	RPC_GETUSERROLES,
	RPC_CLEARSTOREHOUSEPASSWD,
	RPC_CANCHANGEROLENAME,
	RPC_RENAMEROLE,
	RPC_UID2LOGICUID,
	RPC_ROLEID2UID,
	RPC_TRANSACTIONACQUIRE,
	RPC_TRANSACTIONABORT,
	RPC_TRANSACTIONCOMMIT,
	RPC_DBPLAYERREQUITEFRIEND,
	PROTOCOL_DBFRIENDEXTLIST_RE,
	RPC_DBGETMAILLIST,
	RPC_DBGETMAIL,
	RPC_DBGETMAILATTACH,
	RPC_DBSETMAILATTR,
	RPC_DBSENDMAIL,
	RPC_DBDELETEMAIL,
	RPC_DBSENDMASSMAIL,
	RPC_DBAUCTIONOPEN,
	RPC_DBAUCTIONBID,
	RPC_DBAUCTIONGET,
	RPC_DBAUCTIONCLOSE,
	RPC_DBAUCTIONLIST,
	RPC_DBAUCTIONTIMEOUT,
	PROTOCOL_TRANSBUYPOINT,
	PROTOCOL_SYNCSELLINFO,
	RPC_DBSELLPOINT,
	RPC_DBBUYPOINT,
	RPC_DBSYNCSELLINFO,
	RPC_DBSELLTIMEOUT,
	RPC_DBSELLCANCEL,
	RPC_DBTRANSPOINTDEAL,
	RPC_DBBATTLECHALLENGE,
	RPC_DBBATTLELOAD,
	RPC_DBBATTLESET,
	RPC_DBBATTLECHALLENGE,
	RPC_DBBATTLEEND,
	RPC_DBBATTLEMAIL,
	RPC_DBBATTLEBONUS,
	PROTOCOL_ADDCASH,
	PROTOCOL_ADDCASH_RE,
	RPC_CASHSERIAL,
	RPC_GETADDCASHSN,
	RPC_DBSTOCKLOAD,
	RPC_DBSTOCKTRANSACTION,
	RPC_DBSTOCKBALANCE,
	RPC_DBSTOCKCOMMISSION,
	RPC_DBSTOCKCANCEL,
	RPC_DBSETCASHPASSWORD,
	RPC_PUTSPOUSE,
	RPC_DBAUTOLOCKSET,
	RPC_DBAUTOLOCKGET,
	RPC_DBAUTOLOCKSETOFFLINE,
	RPC_QUERYUSERID,
	RPC_FORBIDUSER,
	RPC_DBFORBIDUSER,
	RPC_DBCLEARCONSUMABLE,
	RPC_DBREFWITHDRAWTRANS,
	RPC_DBREFGETREFERRAL,
	RPC_DBREFGETREFERRER,
	RPC_DBREFUPDATEREFERRAL,
	RPC_DBREFUPDATEREFERRER,
	RPC_DBGETREWARD,
	RPC_DBPUTCONSUMEPOINTS,
	RPC_DBPUTREWARDBONUS,
	RPC_DBREWARDMATURE,
	RPC_DBEXCHANGECONSUMEPOINTS,
	PROTOCOL_DEBUGADDCASH,
	RPC_DBPLAYERPOSITIONRESET,
	RPC_DBWEBTRADELOAD,
	RPC_DBWEBTRADELOADSOLD,
	RPC_DBWEBTRADEPREPOST,
	RPC_DBWEBTRADEPRECANCELPOST,
	RPC_DBWEBTRADEPOST,
	RPC_DBWEBTRADECANCELPOST,
	RPC_DBWEBTRADESHELF,
	RPC_DBWEBTRADECANCELSHELF,
	RPC_DBWEBTRADESOLD,
	RPC_DBWEBTRADEPOSTEXPIRE,
	RPC_DBWEBTRADEGETROLESIMPLEINFO,
	RPC_DBSYSAUCTIONCASHTRANSFER,
	RPC_DBSYSAUCTIONCASHSPEND,
	RPC_PUTSERVERDATA,
	RPC_GETSERVERDATA,
	RPC_GETCASHTOTAL,
	RPC_DBFACTIONFORTRESSLOAD,
	RPC_DBPUTFACTIONFORTRESS,
	RPC_DBDELFACTIONFORTRESS,
	RPC_DBCREATEFACTIONFORTRESS,
	RPC_DBFACTIONFORTRESSCHALLENGE,
	RPC_DBGAMETALKROLELIST,
	RPC_DBGAMETALKROLERELATION,
	RPC_DBGAMETALKFACTIONINFO,
	RPC_DBGAMETALKROLESTATUS,
	RPC_DBGAMETALKROLEINFO,
	RPC_DBFORCELOAD,
	RPC_DBPUTFORCE,
	RPC_DBCOUNTRYBATTLEBONUS,
	RPC_FETCHPLAYERDATA,
	RPC_ACTIVATEPLAYERDATA,
	RPC_DELPLAYERDATA,
	RPC_SAVEPLAYERDATA,
	RPC_PLAYERIDENTITYMATCH,
	RPC_FREEZEPLAYERDATA,
	RPC_TOUCHPLAYERDATA,
	RPC_DBUPDATEPLAYERCROSSINFO,
	RPC_DBLOADGLOBALCONTROL,
	RPC_DBPUTGLOBALCONTROL,
	PROTOCOL_ANNOUNCEZONEGROUP,
	RPC_DBUNIQUEDATALOAD,
	RPC_DBUNIQUEDATASAVE,
	RPC_DBPLAYERRENAME,
	RPC_DBROLENAMELIST,
	RPC_DBPLAYERCHANGEGENDER,
	RPC_DBKELOAD,
	RPC_DBKECANDIDATEAPPLY,
	RPC_DBKECANDIDATECONFIRM,
	RPC_DBKEVOTING,
	RPC_DBKEKINGCONFIRM,
	RPC_DBKEDELETEKING,
	RPC_DBKEDELETECANDIDATE,
	RPC_DBPSHOPCREATE,
	RPC_DBPSHOPBUY,
	RPC_DBPSHOPSELL,
	RPC_DBPSHOPCANCELGOODS,
	RPC_DBPSHOPPLAYERBUY,
	RPC_DBPSHOPPLAYERSELL,
	RPC_DBPSHOPSETTYPE,
	RPC_DBPSHOPACTIVE,
	RPC_DBPSHOPMANAGEFUND,
	RPC_DBPSHOPDRAWITEM,
	RPC_DBPSHOPLOAD,
	RPC_DBPSHOPGET,
	RPC_DBPSHOPCLEARGOODS,
	RPC_DBPSHOPTIMEOUT,
	RPC_DBPLAYERGIVEPRESENT,
	RPC_DBPLAYERASKFORPRESENT,
	RPC_DBSYSMAIL3,
	RPC_DBGETPLAYERPROFILEDATA,
	RPC_DBPUTPLAYERPROFILEDATA,
	RPC_DBTANKBATTLEBONUS,
	RPC_DBFACTIONRESOURCEBATTLEBONUS,
	RPC_DBCOPYROLE,
	RPC_DBFACTIONRENAME,
	RPC_DBMNFACTIONAPPLYINFOGET,
	RPC_DBMNFACTIONBATTLEAPPLY,
	PROTOCOL_MNFACTIONINFOUPDATE,
	RPC_DBMNFACTIONAPPLYRESNOTIFY,
	RPC_DBMNSENDBATTLEBONUS,
	RPC_DBMNFACTIONINFOGET,
	RPC_DBMNFACTIONSTATEUPDATE,
	RPC_DBMNFACTIONINFOUPDATE,
	RPC_DBMNDOMAININFOUPDATE,
	RPC_DBMNFACTIONAPPLYINFOPUT,
	RPC_DBMNPUTBATTLEBONUS,
	RPC_DBMNSENDBONUSNOTIFY,
};

GNET::Protocol::Manager::Session::State state_GameDBClient(_state_GameDBClient,
						sizeof(_state_GameDBClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_UniqueNameClient[] = 
{
	RPC_PRECREATEROLE,
	RPC_POSTCREATEROLE,
	RPC_POSTDELETEROLE,
	RPC_PRECREATEFACTION,
	RPC_POSTCREATEFACTION,
	RPC_POSTDELETEFACTION,
	RPC_PRECREATEFAMILY,
	RPC_POSTCREATEFAMILY,
	RPC_POSTDELETEFAMILY,
	RPC_PREPLAYERRENAME,
	RPC_PREFACTIONRENAME,
};

GNET::Protocol::Manager::Session::State state_UniqueNameClient(_state_UniqueNameClient,
						sizeof(_state_UniqueNameClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GRoleDBClient[] = 
{
	RPC_ACCOUNTADDROLE,
	RPC_ACCOUNTDELROLE,
};

GNET::Protocol::Manager::Session::State state_GRoleDBClient(_state_GRoleDBClient,
						sizeof(_state_GRoleDBClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GFactionDeliveryClient[] = 
{
	RPC_ANNOUNCEFACTIONROLEDEL,
	PROTOCOL_BATTLEENTER,
	PROTOCOL_DELFACTIONANNOUNCE,
	PROTOCOL_SENDBATTLECHALLENGE,
	PROTOCOL_CREATEFACTIONFORTRESS,
	PROTOCOL_FACTIONMEMBERUPDATE,
	PROTOCOL_FACTIONINFOUPDATE,
	PROTOCOL_FACTIONMSG,
	PROTOCOL_FACTIONFORBIDUPDATE,
	PROTOCOL_PLAYERSENDMASSMAIL,
	RPC_DBFACTIONRENAME,
};

GNET::Protocol::Manager::Session::State state_GFactionDeliveryClient(_state_GFactionDeliveryClient,
						sizeof(_state_GFactionDeliveryClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_ACClient[] = 
{
	PROTOCOL_ACREMOTECODE,
	PROTOCOL_ACQUESTION,
	PROTOCOL_GMKICKOUTUSER,
	PROTOCOL_ACKICKOUTUSER,
	PROTOCOL_ACREPORTCHEATER,
	PROTOCOL_ACACCUSERE,
};

GNET::Protocol::Manager::Session::State state_ACClient(_state_ACClient,
						sizeof(_state_ACClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GWebTradeClient[] = 
{
	PROTOCOL_POST_RE,
	PROTOCOL_GAMEPOSTCANCEL_RE,
	PROTOCOL_WEBPOSTCANCEL,
	PROTOCOL_SHELF,
	PROTOCOL_SHELFCANCEL,
	PROTOCOL_SOLD,
	PROTOCOL_POSTEXPIRE,
	PROTOCOL_WEBGETROLELIST,
	RPC_QUERYUSERID,
};

GNET::Protocol::Manager::Session::State state_GWebTradeClient(_state_GWebTradeClient,
						sizeof(_state_GWebTradeClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_GameTalkClient[] = 
{
	PROTOCOL_ANNOUNCERESP,
	PROTOCOL_IMKEEPALIVE,
	PROTOCOL_ROLELISTREQ,
	PROTOCOL_ROLERELATIONREQ,
	PROTOCOL_FACTIONINFOREQ,
	PROTOCOL_GAMEDATAREQ,
	PROTOCOL_ROLEINFOREQ,
	PROTOCOL_ROLESTATUSREQ,
	PROTOCOL_ROLESTATUSRESP,
	PROTOCOL_ROLESTATUSUPDATE,
	PROTOCOL_ROLEACTIVATION,
	PROTOCOL_ROLEMSG,
	PROTOCOL_ROLEOFFLINEMESSAGES,
	PROTOCOL_FACTIONMSG,
	PROTOCOL_SYNCTEAMS,
	PROTOCOL_TEAMCREATE,
	PROTOCOL_TEAMDISMISS,
	PROTOCOL_TEAMMEMBERUPDATE,
	PROTOCOL_ROLEENTERVOICECHANNEL,
	PROTOCOL_ROLEENTERVOICECHANNELACK,
	PROTOCOL_ROLELEAVEVOICECHANNEL,
	PROTOCOL_ROLELEAVEVOICECHANNELACK,
};

GNET::Protocol::Manager::Session::State state_GameTalkClient(_state_GameTalkClient,
						sizeof(_state_GameTalkClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_SNSClient[] = 
{
	PROTOCOL_IMKEEPALIVE,
	PROTOCOL_GAMEDATAREQ,
	PROTOCOL_ROLELISTREQ,
	PROTOCOL_ROLERELATIONREQ,
	PROTOCOL_FACTIONINFOREQ,
};

GNET::Protocol::Manager::Session::State state_SNSClient(_state_SNSClient,
						sizeof(_state_SNSClient)/sizeof(GNET::Protocol::Type), 86400);

static GNET::Protocol::Type _state_CentralDeliveryClient[] = 
{
	PROTOCOL_LOADEXCHANGE,
	PROTOCOL_DSANNOUNCEIDENTITY,
	PROTOCOL_SENDDATAANDIDENTITY,
	PROTOCOL_SENDDATAANDIDENTITY_RE,
	PROTOCOL_REMOTELOGINQUERY,
	PROTOCOL_REMOTELOGOUT,
	PROTOCOL_KICKOUTREMOTEUSER_RE,
	PROTOCOL_ACKICKOUTUSER,
	PROTOCOL_GMKICKOUTUSER,
	PROTOCOL_GMSHUTUP,
	PROTOCOL_GETREMOTEROLEINFO_RE,
	PROTOCOL_GETREMOTECNETSERVERCONFIG_RE,
	PROTOCOL_CROSSGUARDNOTIFY,
	PROTOCOL_MNFACTIONCACHEGET_RE,
	PROTOCOL_MNFETCHFILTRATERESULT_RE,
	PROTOCOL_MNFACTIONPROCLAIM_RE,
	PROTOCOL_MNDOMAINSENDBONUSDATA,
	PROTOCOL_MNFETCHTOPLIST_RE,
	PROTOCOL_CROSSCHAT_RE,
	PROTOCOL_CROSSSOLOCHALLENGERANK,
	PROTOCOL_CROSSSOLOCHALLENGERANK_RE,
};

GNET::Protocol::Manager::Session::State state_CentralDeliveryClient(_state_CentralDeliveryClient,
						sizeof(_state_CentralDeliveryClient)/sizeof(GNET::Protocol::Type), 120);

static GNET::Protocol::Type _state_CentralDeliveryServer[] = 
{
	PROTOCOL_LOADEXCHANGE,
	PROTOCOL_DSANNOUNCEIDENTITY,
	PROTOCOL_SENDDATAANDIDENTITY,
	PROTOCOL_SENDDATAANDIDENTITY_RE,
	PROTOCOL_REMOTELOGINQUERY_RE,
	PROTOCOL_KICKOUTREMOTEUSER,
	PROTOCOL_GETREMOTEROLEINFO,
	PROTOCOL_ADDICTIONCONTROL,
	PROTOCOL_GETREMOTECNETSERVERCONFIG,
	PROTOCOL_MNFACTIONCACHEGET,
	PROTOCOL_MNFACTIONINFOUPDATE,
	PROTOCOL_MNFACTIONPROCLAIM,
	PROTOCOL_MNFETCHFILTRATERESULT,
	PROTOCOL_MNDOMAINSENDBONUSDATA_RE,
	PROTOCOL_MNFETCHTOPLIST,
	PROTOCOL_CROSSCHAT,
	PROTOCOL_CROSSSOLOCHALLENGERANK,
	PROTOCOL_CROSSSOLOCHALLENGERANK_RE,
};

GNET::Protocol::Manager::Session::State state_CentralDeliveryServer(_state_CentralDeliveryServer,
						sizeof(_state_CentralDeliveryServer)/sizeof(GNET::Protocol::Type), 120);


};

