
#ifndef __GNET_TRANSACTIONACQUIRE_HPP
#define __GNET_TRANSACTIONACQUIRE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "transactiontimeout"
#include "transactionid"

namespace GNET
{

class TransactionAcquire : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "transactionacquire"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TransactionTimeout *arg = (TransactionTimeout *)argument;
		// TransactionId *res = (TransactionId *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// TransactionTimeout *arg = (TransactionTimeout *)argument;
		// TransactionId *res = (TransactionId *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
