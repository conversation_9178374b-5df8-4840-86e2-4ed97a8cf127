
#ifndef __GNET_DBPUTFORCE_HPP
#define __GNET_DBPUTFORCE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbputforcearg"
#include "dbputforceres"

#include "forcemanager.h"

namespace GNET
{

class DBPutForce : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbputforce"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBPutForceArg *arg = (DBPutForceArg *)argument;
		// DBPutForceRes *res = (DBPutForceRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		//DBPutForceArg *arg = (DBPutForceArg *)argument;
		DBPutForceRes *res = (DBPutForceRes *)result;
		DEBUG_PRINT("dbputforce: rpc return. retcode=%d", res->retcode);

		ForceManager::GetInstance()->OnDBPutForce(res->retcode);
	}

	void OnTimeout()
	{
		// TODO Client Only
		Log::log(LOG_ERR,"dbputforce: timeout.\n");
		ForceManager::GetInstance()->OnDBPutForce(ERR_TIMEOUT);
	}

};

};
#endif
