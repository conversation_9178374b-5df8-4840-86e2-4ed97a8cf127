
#ifndef __GNET_POSTDELETEFAMILY_HPP
#define __GNET_POSTDELETEFAMILY_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "postdeletefamilyarg"
#include "postdeletefamilyres"

namespace GNET
{

class PostDeleteFamily : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "postdeletefamily"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// PostDeleteFamilyArg *arg = (PostDeleteFamilyArg *)argument;
		// PostDeleteFamilyRes *res = (PostDeleteFamilyRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// PostDeleteFamilyArg *arg = (PostDeleteFamilyArg *)argument;
		// PostDeleteFamilyRes *res = (PostDeleteFamilyRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
