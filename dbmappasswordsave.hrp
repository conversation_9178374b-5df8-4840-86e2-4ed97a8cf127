
#ifndef __GNET_DBMAPPASSWORDSAVE_HPP
#define __GNET_DBMAPPASSWORDSAVE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbmappasswordsavearg"
#include "dbmappasswordsaveres"

namespace GNET
{

class DBMapPasswordSave : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbmappasswordsave"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBMapPasswordSaveArg *arg = (DBMapPasswordSaveArg *)argument;
		// DBMapPasswordSaveRes *res = (DBMapPasswordSaveRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// DBMapPasswordSaveArg *arg = (DBMapPasswordSaveArg *)argument;
		// DBMapPasswordSaveRes *res = (DBMapPasswordSaveRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
