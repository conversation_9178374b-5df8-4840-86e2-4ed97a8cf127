
#ifndef __GNET_PUTROLEPOCKET_HPP
#define __GNET_PUTROLEPOCKET_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "rolepocketpair"


namespace GNET
{

class PutRolePocket : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "putrolepocket"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// RolePocketPair *arg = (RolePocketPair *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// RolePocketPair *arg = (RolePocketPair *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
