
#ifndef __GNET_DBSELLTIMEOUT_HPP
#define __GNET_DBSELLTIMEOUT_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "sellid"
#include "dbsyncsellinfores"

namespace GNET
{
class DBSellTimeout : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbselltimeout"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
	}
	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
	}

	void OnTimeout(Rpc::Data *argument)
	{
	}

};

};
#endif
