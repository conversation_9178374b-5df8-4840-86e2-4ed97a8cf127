
#ifndef __GNET_GETROLEBASESTATUS_HPP
#define __GNET_GETROLEBASESTATUS_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "roleid"
#include "getrolebasestatusres"

#include "groleinventory"

#include "rolelist_re.hpp"
#include "dbdeleterole.hrp"
#include "gdeliveryserver.hpp"
#include "gamedbclient.hpp"
#include "gproviderserver.hpp"
namespace GNET
{

class GetRoleBaseStatus : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getrolebasestatus"
#undef	RPC_BASECLASS
	unsigned int save_link_sid;
	unsigned int save_localsid;
	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_BDB
		RoleId *arg = (RoleId *)argument;
		GetRoleBaseStatusRes *res = (GetRoleBaseStatusRes *)result;
		Marshal::OctetsStream key, value;
		key << *arg;
		res->retcode = DBBuffer::buf_find( "base", key, value );
		if( 0 == res->retcode )
			value >> res->base;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
	//	RoleId *arg = (RoleId *)argument;
	//	GetRoleBaseStatusRes *res = (GetRoleBaseStatusRes *)result;
		

	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
