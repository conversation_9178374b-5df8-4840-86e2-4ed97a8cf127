//------------------------------------------------------------------------------------------------------------------------
//--ARENA AURURE (C) 2022 AnotherCompiler
//------------------------------------------------------------------------------------------------------------------------
#ifndef __GNET_ARENAMANAGER_H
#define __GNET_ARENAMANAGER_H

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <iostream>
#include <cstring>
#include <algorithm>
#include <mutex>
#include <vector>
#include <condition_variable>
#include <ctime>
#include <sys/stat.h>
#include <sys/time.h>
#include "rpcdefs.h"
#include <unordered_map>

namespace GNET
{
	// Forward declarations
	class LuaManager;
	class ArenaBattleStart;

	class ArenaTimerManager
	{
	public:
		enum
		{
			
			MIN  					= 60,
			HOUR 					= MIN*60,
			DAY  					= HOUR*24,
			WEEK 					= DAY*7,
			
			MAX_ROLE_TIMER 			= 30000,
			
		};
	private:
		struct ROLEDATA
		{
			int roleid;
			time_t tick;
			ROLEDATA(int roleid, time_t tick)
			{
				this->roleid = roleid;
				this->tick = tick;
			}
		};
	private:
		static std::vector <ROLEDATA> roledata;
		static int tick;
		static time_t last_message_time_1x1_1;
		static time_t last_message_time_1x1_2;
		static time_t last_message_time_1x1_3;
		static time_t last_message_time_3x3_1;
		static time_t last_message_time_3x3_2;
		static time_t last_message_time_3x3_3;
		static time_t last_message_time_6x6_1;
		static time_t last_message_time_6x6_2;
		static time_t last_message_time_6x6_3;
	public:
		void Init();
		void HeartBeat();
		//bool CheckEnable(int roleid);
		bool CheckEnable(int roleid, int mode);
		void SendEnableMessage();
		void AddRole(int roleid);
		bool CheckRole(int roleid);
		
		
		static ArenaTimerManager * GetInstance()
		{
			if (!instance)
			instance = new ArenaTimerManager();
			return instance;
		}
		static ArenaTimerManager * instance;
	};
	
	class ArenaManager
	{
	public:
		enum
		{
			DEF_ROLEID = 1024,
			MAX_TEAM = 1024,
			MAX_ROLE_TEAM = 6,
			
			MODE_1X1 = 1,
			MODE_3X3 = 2,
			MODE_6X6 = 3,
			
			STATE_JOIN = 0,
			STATE_BATTLE = 1,
			STATE_END = 2,
			
			MSG_ADD_USER_FAIL		= 0,
			MSG_ADD_USER_SUCCES		= 1,
			MSG_ADD_USER_IS_JOIN	= 2,
			MSG_ADD_USER_IS_BATTLE	= 3,
			
			MSG_DEL_USER_FAIL		= 4,
			MSG_DEL_USER_SUCCES		= 5,
			MSG_DEL_USER_TIMEOUT	= 6,
			MSG_DEL_USER_TAG		= 7,
			MSG_DEL_USER_IS_BATTLE	= 8,
			
			MSG_START_USER_RED		= 9,
			MSG_START_USER_BLUE		= 10,
			
			MSG_BATTLE_USER_WIN		= 11,
			MSG_BATTLE_USER_LOSE	= 12,
			MSG_BATTLE_USER_DRAW	= 13,
			
			MSG_TIME_DISTABLE		= 14,

			MSG_TIME_DISTABLE_1X1	= 15,
			MSG_TIME_DISTABLE_3X3	= 16,
			MSG_TIME_DISTABLE_6X6	= 17,
			MSG_ARENA_1X1_OPEN		= 18,
			MSG_ARENA_3X3_OPEN		= 19,
			MSG_ARENA_6X6_OPEN		= 20,

			MSG_FAIL_HWID			= 21,

			MSG_DEL_TEAM_USER_TIMEOUT = 22,
			
			FLAG_RED				= 0,
			FLAG_BLUE				= 1,
			FLAG_WIN				= 0,
			FLAG_LOSE				= 1,
		};
	public:
	#pragma pack(push, 1)

		struct TEAMDATA
		{
			int id;
			int battle_id;
			time_t tick;
			char mode;
			char state;
			char red;
			int score;

			bool isRandomTeam;
			
			std::vector<int> role;
			
			unsigned int GET_MAX_PLAYERS() const
			{
				unsigned int count = 0;
				switch (this->mode)
				{
				case MODE_1X1:
					count = 1;
					break;
				case MODE_3X3:
					count = 3;
					break;
				case MODE_6X6:
					count = 6;
					break;
				default:
					break;
				}
				return count;
			}
			
			TEAMDATA(int id, char mode, time_t tick, int score, bool isRandomTeam = false)
				: id(id), battle_id(0), tick(tick), mode(mode), state(STATE_JOIN), red(0xFF), score(score), isRandomTeam(isRandomTeam)
			{
				role.reserve(MAX_ROLE_TEAM);
			}
			
			void CLEAR_ROLELIST()
			{
				role.clear();
			}
			
			void ADD_ROLE(int roleid)
			{
				if (role.size() < GET_MAX_PLAYERS() && std::find(role.begin(), role.end(), roleid) == role.end())
				{
					role.push_back(roleid);
				}
			}
			
			void DEL_ROLE(int roleid)
			{
				auto it = std::find(role.begin(), role.end(), roleid);
				if (it != role.end())
				{
					role.erase(it);
				}
			}
			
			unsigned int GET_ROLE(unsigned int i) const
			{
				if (i < role.size() && i < GET_MAX_PLAYERS())
				{
					return role[i];
				}
				return 0;
			}
			
			bool CHECK_ROLE(int roleid) const
			{
				return std::find(role.begin(), role.end(), roleid) != role.end();
			}
			
			unsigned int COUNT_ROLE() const
			{
				return role.size();
			}
			
			unsigned int GET_SCORE() const
			{
				return score;
			}
		};
	
	#pragma pack(pop)
	private:
		static bool log;
		static time_t tick;
		static pthread_mutex_t arena_mutex;
		static std::vector<TEAMDATA> teamdata; 
		
		static std::vector<int> v1x1;
		static std::vector<int> v3x3;
		static std::vector<int> v6x6;

		static std::vector<int> random_team_3x3;
		static std::vector<int> random_role_3x3;


		static std::vector<int> random_team_6x6;
		static std::vector<int> random_role_6x6;

		static std::unordered_map<int, int> random_role_3x3_ticks;
		static std::unordered_map<int, int> random_role_6x6_ticks;


		static int team_counter;
	public:

		int  GetTeam(int xid);
		std::vector<int> GetRedTeamRoles(int battle_id);
		std::vector<int> GetBlueTeamRoles(int battle_id);
		void SendEndDiscordWebHook(int battle_id, char battle_mode, char red_win, int tick_end, int damage_red, int damage_blue);
		unsigned int CountTeamJoin();
		unsigned int CountTeamBattle();
		unsigned int CountTeamEnd();
		int  SearchRole(int roleid);
		int  AddTeam(int xid, char mode, int score);
		int  DelTeam(int xid);
		int  AddRole(int xid, int roleid);
		int  DelRole(int roleid);

		void AutoClean();
		void SetPool();
		void BattleRelease(int battle , int id1, int id2, char mode);
		void BattleStart();
		void BattleResult(int roleid, char battle_mode, char is_win, int score);
		void BattleEnd(int battle_id, char battle_mode, char red_win, int score, int damage_red, int damage_blue);
		void Update();
		void Init();
		void HeartBeat();
		void CheckAndEndInactiveBattles();
		unsigned long long GetHwid(int roleid);

		// Structure for pthread-based BattleRelease
		struct BattleReleaseThreadData {
			ArenaManager* arena_manager;
			ArenaBattleStart* abs;
			int team_id;
			int battle_idx;
			char mode;
			LuaManager* lua;
			bool is_red_team;
		};

		// Static thread function for BattleRelease
		static void* ProcessTeamBattleRelease(void* arg);

		//random
		void RandomSetPool();
        void RandomCreateTeams();
		void RandomBattleStart();
		int AddSoloRole3x3(int roleid);
		int DelSoloRole3x3(int roleid);
		int AddSoloRole6x6(int roleid);
		int DelSoloRole6x6(int roleid);
		
		static ArenaManager * GetInstance()
		{
			if (!instance)
			instance = new ArenaManager();
			return instance;
		}
		static ArenaManager * instance;
	};
	
	class ArenaTimer : public Thread::Runnable
	{
		int update_time;
	public:
		ArenaTimer(int _time,int _proir=1) : Runnable(_proir),update_time(_time) { }
		void Run();
	private:
		void UpdateTimer();
	};
	
	static auto TeamComp = [](const ArenaManager::TEAMDATA & lhs, const ArenaManager::TEAMDATA & rhs)->bool
	{
		return lhs.GET_SCORE() < rhs.GET_SCORE();
	};

};

#endif