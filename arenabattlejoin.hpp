
#ifndef __GNET_ARENABATTLEJOIN_HPP
#define __GNET_ARENABATTLEJOIN_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#include "arenamanager.hpp"
#include "arenabattlefailed.hpp"

namespace GNET
{

class ArenaBattleJoin : public GNET::Protocol
{
	#include "arenabattlejoin"

	void SendFailed()
	{
		for ( unsigned int i = 0; i < playerlist.size() && i < 6; i++ )
		{
			
		}
	}

	void Process(Manager *manager, Manager::Session::ID sid)
	{
		ArenaTimerManager * arena_tm = ArenaTimerManager::GetInstance();
		ArenaManager * arena = ArenaManager::GetInstance();
		
		if(roleid < 1024 ) 
		{
			return;
		}
		
		if ( !arena_tm->CheckEnable(roleid, this->mode) )
		{
			return;
		}
		
		for ( unsigned int i = 0; i < playerlist.size() && i < 6; i++ )
		{
			if ( !arena_tm->CheckRole(roleid) )
			{
				return;
			}
		}
		
		if( UserContainer::GetInstance().FindRoleOnline(roleid) )
		{
			switch (this->mode)
			{
			case ArenaManager::MODE_1X1:
			{
				arena->AddTeam(roleid,ArenaManager::MODE_1X1, strong);
				for ( unsigned int i = 0; i < playerlist.size() && i < 1; i++ )
				{
					arena->AddRole(roleid, playerlist[i] );
				}
				break;
			}
			case ArenaManager::MODE_3X3:
			{
				if(playerlist.size() == 0 || (playerlist.size() == 1 && playerlist[0] == roleid))
				{
					int res = arena->AddSoloRole3x3(roleid);
                    if (res == ArenaManager::MSG_ADD_USER_SUCCES)
                    {
                        printf("ARENA::Process: Added roleid = %d to solo 3x3 queue\n", roleid);
                    }
                    else
                    {
                        printf("ARENA::Process: Failed to add roleid = %d to solo 3x3 queue\n", roleid);
                        return;
                    }
				}
				else
				{
					arena->AddTeam(roleid,ArenaManager::MODE_3X3, strong);
					for ( unsigned int i = 0; i < playerlist.size() && i < 3; i++ )
					{
						arena->AddRole(roleid, playerlist[i] );
					}
				}
				break;
			}
			case ArenaManager::MODE_6X6:
			{
				if(playerlist.size() == 0 || (playerlist.size() == 1 && playerlist[0] == roleid))
				{
					int res = arena->AddSoloRole6x6(roleid);
                    if (res == ArenaManager::MSG_ADD_USER_SUCCES)
                    {
                        printf("ARENA::Process: Added roleid = %d to solo 6x6 queue\n", roleid);
                    }
                    else
                    {
                        printf("ARENA::Process: Failed to add roleid = %d to solo 6x6 queue\n", roleid);
                        return;
                    }
				}
				else
				{
					arena->AddTeam(roleid,ArenaManager::MODE_6X6, strong);
					for ( unsigned int i = 0; i < playerlist.size() && i < 6; i++ )
					{
						arena->AddRole(roleid, playerlist[i] );
					}
				}
				break;
			}
			default:
				break;
			}

		}
	}
};

};

#endif
