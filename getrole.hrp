
#ifndef __GNET_GETROLE_HPP
#define __GNET_GETROLE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_DB
#include "dbbuffer.h"
#endif
#include "rolearg"
#include "roleres"

namespace GNET
{

class GetRole : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getrole"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_DB
		RoleArg *arg = (RoleArg *)argument;
		RoleRes *res = (RoleRes *)result;
		Marshal::OctetsStream key, value;
		key << *arg;
		res->retcode = DBBuffer::buf_find( "base", key, value );
		if( 0 == res->retcode )
			value >> res->value;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// RoleArg *arg = (RoleArg *)argument;
		// RoleRes *res = (RoleRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
