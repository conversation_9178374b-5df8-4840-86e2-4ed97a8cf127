
#ifndef __GNET_GETLOTTERYITEMS_HPP
#define __GNET_GETLOTTERYITEMS_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "getlotteryitemsarg"
#include "getlotteryitemsres"

namespace GNET
{

class GetLotteryItems : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getlotteryitems"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// GetLotteryItemsArg *arg = (GetLotteryItemsArg *)argument;
		// GetLotteryItemsRes *res = (GetLotteryItemsRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetLotteryItemsArg *arg = (GetLotteryItemsArg *)argument;
		// GetLotteryItemsRes *res = (GetLotteryItemsRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
