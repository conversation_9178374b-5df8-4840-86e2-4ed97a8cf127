
#ifndef __GNET_SETTREASUREDATA_HPP
#define __GNET_SETTREASUREDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "settreasuredataarg"
#include "settreasuredatares"

namespace GNET
{

class SetTreasureData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "settreasuredata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// SetTreasureDataArg *arg = (SetTreasureDataArg *)argument;
		// SetTreasureDataRes *res = (SetTreasureDataRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// SetTreasureDataArg *arg = (SetTreasureDataArg *)argument;
		// SetTreasureDataRes *res = (SetTreasureDataRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
