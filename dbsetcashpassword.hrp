
#ifndef __GNET_DBSETCASHPASSWORD_HPP
#define __GNET_DBSETCASHPASSWORD_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#include "cashpasswordset_re.hpp"

#include "dbsetcashpasswordarg"
#include "dbsetcashpasswordres"

namespace GNET
{

class DBSetCashPassword : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbsetcashpassword"
#undef	RPC_BASECLASS
	unsigned int save_sid;
	unsigned int save_localsid;
	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBSetCashPasswordArg *arg = (DBSetCashPasswordArg *)argument;
		// DBSetCashPasswordRes *res = (DBSetCashPasswordRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		DBSetCashPasswordArg *arg = (DBSetCashPasswordArg *)argument;
		DBSetCashPasswordRes *res = (DBSetCashPasswordRes *)result;

		if (res->retcode == ERR_SUCCESS)
		{
			StockExchange::Instance()->SetCashPassword(arg->userid, arg->password);
		}
		else
		{
			Log::log(LOG_ERR, "DBSetCashPassword, critical error, userid=%d, password=%d, retcode=%d"
					, arg->userid, arg->password.size(), res->retcode);
		}

		if(!arg->source) // Set password by player
		{
			GDeliveryServer* dsm=GDeliveryServer::GetInstance();
			dsm->Send(save_sid, CashPasswordSet_Re(res->retcode, save_localsid));
		}

	}

        void OnTimeout(Rpc::Data *argument)
	{
		DBSetCashPasswordArg *arg = (DBSetCashPasswordArg *)argument;
		if(!arg->source)
		{
			GDeliveryServer* dsm=GDeliveryServer::GetInstance();
			dsm->Send(save_sid, CashPasswordSet_Re(ERR_COMMUNICATION, save_localsid));
		}
	}

};

};
#endif
