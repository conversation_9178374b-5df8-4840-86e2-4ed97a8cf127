
#ifndef __GNET_DBMNFACTIONAPPLYINFOPUT_HPP
#define __GNET_DBMNFACTIONAPPLYINFOPUT_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbmnfactionapplyinfoputarg"
#include "dbmnfactionapplyinfoputres"

namespace GNET
{

class DBMNFactionApplyInfoPut : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbmnfactionapplyinfoput"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBMNFactionApplyInfoPutArg *arg = (DBMNFactionApplyInfoPutArg *)argument;
		// DBMNFactionApplyInfoPutRes *res = (DBMNFactionApplyInfoPutRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBMNFactionApplyInfoPutArg *arg = (DBMNFactionApplyInfoPutArg *)argument;
		DBMNFactionApplyInfoPutRes *res = (DBMNFactionApplyInfoPutRes *)result;
		DEBUG_PRINT("file=%s\tfunc=%s\tline=%d\tretcode=%d\n", __FILE__, __FUNCTION__, __LINE__, res->retcode);

	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
