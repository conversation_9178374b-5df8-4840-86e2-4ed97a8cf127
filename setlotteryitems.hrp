
#ifndef __GNET_SETLOTTERYITEMS_HPP
#define __GNET_SETLOTTERYITEMS_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "setlotteryitemsarg"
#include "setlotteryitemsres"

namespace GNET
{

class SetLotteryItems : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "setlotteryitems"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// SetLotteryItemsArg *arg = (SetLotteryItemsArg *)argument;
		// SetLotteryItemsRes *res = (SetLotteryItemsRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// SetLotteryItemsArg *arg = (SetLotteryItemsArg *)argument;
		// SetLotteryItemsRes *res = (SetLotteryItemsRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
