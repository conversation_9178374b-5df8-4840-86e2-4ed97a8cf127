
#ifndef __GNET_DBKELOAD_HPP
#define __GNET_DBKELOAD_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbkeloadarg"
#include "dbkeloadres"

#include "kingelection.h"
#include "gamedbclient.hpp"

namespace GNET
{

class DBKELoad : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbkeload"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBKELoadArg *arg = (DBKELoadArg *)argument;
		// DBKELoadRes *res = (DBKELoadRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		//DBKELoadArg *arg = (DBKELoadArg *)argument;
		DBKELoadRes *res = (DBKELoadRes *)result;
		DEBUG_PRINT("dbkeload: rpc return. retcode=%d", res->retcode);
		
		if(res->retcode == ERR_SUCCESS)
			KingElection::GetInstance().OnDBLoad(res->detail);
	}

	void OnTimeout()
	{
		// TODO Client Only
		DBKELoadArg *arg = (DBKELoadArg *)argument;
		Log::log(LOG_ERR,"dbkeload: timeout.\n");
		GameDBClient::GetInstance()->SendProtocol( Rpc::Call(RPC_DBKELOAD,arg) );
	}

};

};
#endif
