
#ifndef __GNET_PUTSERVERDATA_HPP
#define __GNET_PUTSERVERDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "putserverdataarg"


namespace GNET
{

class PutServerData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "putserverdata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// PutServerDataArg *arg = (PutServerDataArg *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// PutServerDataArg *arg = (PutServerDataArg *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
