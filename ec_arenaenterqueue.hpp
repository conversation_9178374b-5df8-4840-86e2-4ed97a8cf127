
#ifndef __GNET_EC_ARENAENTERQUEUE_HPP
#define __GNET_EC_ARENAENTERQUEUE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "ec_arenamanager.h"

namespace GNET
{

class EC_ArenaEnterQueue : public GNET::Protocol
{
	#include "ec_arenaenterqueue"

	void Process(Manager *manager, Manager::Session::ID sid)
	{
		printf("[EC_ArenaEnterQueue::Process] PROTOCOLO RECEBIDO! ID=%d, roleid=%d, mode=%d, type=%d, members=%zu\n", 
			GetType(), roleid, mode, type, members.size());
			
		// Validar combinação mode/type/members
		bool valid = false;
		
		switch (mode) {
			case 1: // 1x1
				if (battletype == 0 && members.size() == 1) {
					valid = true;
				}
				break;
			case 2: // 2x2
				if ((battletype == 0 && members.size() == 1) || (battletype == 1 && members.size() == 2)) {
					valid = true;
				}
				break;
			case 3: // 3x3
				if (battletype == 1 && members.size() == 3) {
					valid = true;
				}
				break;
			case 4: // 6x6
				if (battletype == 1 && members.size() == 6) {
					valid = true;
				}
				break;
		}
		
		if (!valid) {
			printf("[ERROR] EC_ArenaEnterQueue: combinação inválida - mode=%d, type=%d, members=%zu\n", 
				mode, type, members.size());
			return;
		}
		
		ArenaOfAuroraManager::GetInstance()->OnStartSearch(roleid, cls, name, members, mode, type);
	}
};

};

#endif
