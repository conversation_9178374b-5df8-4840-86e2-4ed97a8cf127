
#ifndef __GNET_DBMNDOMAINBONUS_HPP
#define __GNET_DBMNDOMAINBONUS_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbmndomainbonusarg"
#include "dbmndomainbonusres"

namespace GNET
{

class DBMNDomainBonus : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbmndomainbonus"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBMNDomainBonusArg *arg = (DBMNDomainBonusArg *)argument;
		// DBMNDomainBonusRes *res = (DBMNDomainBonusRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// DBMNDomainBonusArg *arg = (DBMNDomainBonusArg *)argument;
		// DBMNDomainBonusRes *res = (DBMNDomainBonusRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
