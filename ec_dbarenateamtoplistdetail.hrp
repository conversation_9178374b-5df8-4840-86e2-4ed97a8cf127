
#ifndef __GNET_EC_DBARENATEAMTOPLISTDETAIL_HPP
#define __GNET_EC_DBARENATEAMTOPLISTDETAIL_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "ec_dbarenateamtoplistdetailarg"
#include "ec_dbarenateamtoplistdetailres"

#include "ec_arenamanager.h"

namespace GNET
{

class EC_DBArenaTeamTopListDetail : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "ec_dbarenateamtoplistdetail"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// EC_DBArenaTeamTopListDetailArg *arg = (EC_DBArenaTeamTopListDetailArg *)argument;
		// EC_DBArenaTeamTopListDetailRes *res = (EC_DBArenaTeamTopListDetailRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		EC_DBArenaTeamTopListDetailArg *arg = (EC_DBArenaTeamTopListDetailArg *)argument;
		EC_DBArenaTeamTopListDetailRes *res = (EC_DBArenaTeamTopListDetailRes *)result;
		ArenaOfAuroraManager::GetInstance()->EC_ArenaTeamTopListDetail_Re( arg->roleid, res->teamid, res->playerlist );
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
