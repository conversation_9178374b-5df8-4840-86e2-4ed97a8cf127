
#ifndef __GNET_DBTRANSPOINTDEAL_HPP
#define __GNET_DBTRANSPOINTDEAL_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "roleid"
#include "dbtranspointdealres"
namespace GNET
{

class DBTransPointDeal : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbtranspointdeal"
#undef	RPC_BASECLASS
	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// RoleId *arg = (RoleId *)argument;
		// DBTransPointDealRes *res = (DBTransPointDealRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{

	}

	void OnTimeout(Rpc::Data *argument)
	{
	}

};

};
#endif
