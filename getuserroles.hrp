
#ifndef __GNET_GETUSERROLES_HPP
#define __GNET_GETUSERROLES_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "getuserrolesarg"
#include "getuserrolesres"

namespace GNET
{

class GetUserRoles : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getuserroles"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetUserRolesArg *arg = (GetUserRolesArg *)argument;
		// GetUserRolesRes *res = (GetUserRolesRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
