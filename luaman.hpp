//---------------------------------------------------------------------------------------------------------------------------
//--PW LUA SCRIPT GDELIVERYD (C) DeadRaky 2022
//---------------------------------------------------------------------------------------------------------------------------
#ifndef __GNET_LUAMANAGER_H
#define __GNET_LUAMANAGER_H

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <iostream>
#include <cstring>
#include <mutex>
#include <sys/stat.h>
#include <sys/time.h>
#include <lua.hpp>
#include "rpcdefs.h"
#include "arenamanager.hpp"

namespace GNET
{ 

	class LuaManager
	{
	private:
		enum
		{
			MIN  					= 60,
			HOUR 					= MIN*60,
			DAY  					= HOUR*24,
			WEEK 					= DAY*7,
			
			TYPE_CHAR 				= 0,
			TYPE_SHORT 				= 1,
			TYPE_INT 				= 2,
			TYPE_INT64 				= 3,
			TYPE_FLOAT 				= 4,
			TYPE_DOUBLE 			= 5,
				
			CHAT_SINGLE				= 0,
			CHAT_INSTANCE			= 1,
			CHAT_WORLD				= 2,
			CHAT_MAX				= 3,
			
			MAX_TAGS				= 128,
			MAX_SKILL				= 128,
			MAX_ITEM				= 128,
			
			WAR_TYPE_MAX 			= 3,
			
		};
	private:
#pragma pack(push, 1)
		struct CONFIG
		{
			int COUNTRY_MAX_BONUS = 10000;
			int COUNTRY_PLAYERS_CNT[WAR_TYPE_MAX] = {30, 20, 40};

			time_t ARENA_MSG_TIME 		= 30;
			time_t ARENA_WHITE_TIME 	= MIN*10;
			time_t ARENA_SEARCH_TEAM_TIME 	= MIN*10;
			time_t ARENA_UPDATE_TIME 	= MIN*3;
			time_t ARENA_BATTLE_TIME 	= MIN*18;
			time_t ARENA_CHECK_OPEN_TIME = MIN*3;
			time_t ARENA_MESSAGE_OPEN_INTERVAL = MIN*3;

			int ARENA_1X1_SUNDAY = 1;
			int ARENA_1X1_MONDAY = 1;
			int ARENA_1X1_TUESDAY = 1;
			int ARENA_1X1_WEDNESDAY = 1;
			int ARENA_1X1_THURSDAY = 1;
			int ARENA_1X1_FRIDAY = 1;
			int ARENA_1X1_SATURDAY = 1;

			int ARENA_3X3_SUNDAY = 1;
			int ARENA_3X3_MONDAY = 1;
			int ARENA_3X3_TUESDAY = 1;
			int ARENA_3X3_WEDNESDAY = 1;
			int ARENA_3X3_THURSDAY = 1;
			int ARENA_3X3_FRIDAY = 1;
			int ARENA_3X3_SATURDAY = 1;

			int ARENA_6X6_SUNDAY = 1;
			int ARENA_6X6_MONDAY = 1;
			int ARENA_6X6_TUESDAY = 1;
			int ARENA_6X6_WEDNESDAY = 1;
			int ARENA_6X6_THURSDAY = 1;
			int ARENA_6X6_FRIDAY = 1;
			int ARENA_6X6_SATURDAY = 1;


			time_t ARENA_1X1_TIME_START_1	= HOUR*9;
			time_t ARENA_1X1_TIME_END_1		= HOUR*12;
			time_t ARENA_1X1_TIME_START_2	= HOUR*15;
			time_t ARENA_1X1_TIME_END_2		= HOUR*17;
			time_t ARENA_1X1_TIME_START_3	= HOUR*20;
			time_t ARENA_1X1_TIME_END_3		= HOUR*22;

			time_t ARENA_3X3_TIME_START_1	= HOUR*9;
			time_t ARENA_3X3_TIME_END_1		= HOUR*12;
			time_t ARENA_3X3_TIME_START_2	= HOUR*15;
			time_t ARENA_3X3_TIME_END_2		= HOUR*17;
			time_t ARENA_3X3_TIME_START_3	= HOUR*20;
			time_t ARENA_3X3_TIME_END_3		= HOUR*22;

			time_t ARENA_6X6_TIME_START_1	= HOUR*9;
			time_t ARENA_6X6_TIME_END_1		= HOUR*12;
			time_t ARENA_6X6_TIME_START_2	= HOUR*15;
			time_t ARENA_6X6_TIME_END_2		= HOUR*17;
			time_t ARENA_6X6_TIME_START_3	= HOUR*20;
			time_t ARENA_6X6_TIME_END_3		= HOUR*22;


			time_t ARENA_MATCH_COOLDOWN	= MIN*2;
			
			float ARENA_RATE			= 1.0f;
			float ARENA_RATE_TEAM		= 1.0f;
			
			int ARENA_TAGS[MAX_TAGS] = {119 , 127, 128, 129, 0};

			int HIDE_TAGS[MAX_TAGS] = {0};

			int arena_hwid_check_enable = 1;

			int dont_save_ip_user_id = 0;
			
			struct
			{
				int tag;
				int limit;
			}	HW_TAGS[MAX_TAGS];
			
			void INIT()
			{
				memset(HW_TAGS, 0x00, sizeof(HW_TAGS));				
			}
			
			size_t IS_TRUE_TAG_LIMITER(int tag)
			{
				for (size_t i = 0; HW_TAGS[i].tag && i < 128 ; i++)
					if (HW_TAGS[i].tag == tag)
						return HW_TAGS[i].limit;
				return 0;
			}

			bool IS_TRUE_TAG_ARENA(int tag)
			{
				for (size_t i = 0; ARENA_TAGS[i] && i < 128 ; i++)
					if (ARENA_TAGS[i] == tag)
						return true;
				return false;
			}

			bool IS_TRUE_ARENA_WEEK(int mode, int week)
			{
				//printf("IS_TRUE_ARENA_WEEK, mode:%d week:%d\n", mode, week);

				switch(mode)
				{
					case ArenaManager::MODE_1X1:
						switch(week)
						{
							case 0: return ARENA_1X1_SUNDAY;
							case 1: return ARENA_1X1_MONDAY;
							case 2: return ARENA_1X1_TUESDAY;
							case 3: return ARENA_1X1_WEDNESDAY;
							case 4: return ARENA_1X1_THURSDAY;
							case 5: return ARENA_1X1_FRIDAY;
							case 6: return ARENA_1X1_SATURDAY;
						}
						break;
					case ArenaManager::MODE_3X3:
						switch(week)
						{
							case 0: return ARENA_3X3_SUNDAY;
							case 1: return ARENA_3X3_MONDAY;
							case 2: return ARENA_3X3_TUESDAY;
							case 3: return ARENA_3X3_WEDNESDAY;
							case 4: return ARENA_3X3_THURSDAY;
							case 5: return ARENA_3X3_FRIDAY;
							case 6: return ARENA_3X3_SATURDAY;
						}
						break;
					case ArenaManager::MODE_6X6:
						switch(week)
						{
							case 0: return ARENA_6X6_SUNDAY;
							case 1: return ARENA_6X6_MONDAY;
							case 2: return ARENA_6X6_TUESDAY;
							case 3: return ARENA_6X6_WEDNESDAY;
							case 4: return ARENA_6X6_THURSDAY;
							case 5: return ARENA_6X6_FRIDAY;
							case 6: return ARENA_6X6_SATURDAY;
						}
						break;
					default:
						return false;
				}
			}

		};
#pragma pack(pop)
	private:
		static lua_State* L;
		static pthread_mutex_t lua_mutex;
		static const char * FName;
		static time_t reload_tm;
		static size_t tick;
		static CONFIG config;
		static bool lua_debug;
		
	public:
		static void game__Patch(long long address, int type, double value);
		static double game__Get(long long address, int type, long long offset);
		static void game__SingleChatMsg(int roleid, int channel, const char * utf8_msg);
		static void game__ChatBroadCast(int roleid, int channel, const char * utf8_msg);
		static int game__CheckRoleGsArena(int roleid);

		static int game__CheckRoleHideTag(int roleid);
		static void game__SendDiscordWebHook(const std::string& webhook, const std::string& message);
		static void sendDiscordWebHookHelper(const std::string& webhook, const std::string& message);	

		static void game__SendDiscordEmbedWebHook(const std::string& webhook, const std::string& username,const std::string& avatar_url,const std::string& title,const std::string& message,const std::string& color,const std::string& thumbnail_url,const std::string& footer_url,const std::string& footer_text,const std::string& timestamp);
		static void sendDiscordEmbedWebHookHelper(const std::string& webhook, const std::string& username,const std::string& avatar_url,const std::string& title,const std::string& message,const std::string& color,const std::string& thumbnail_url,const std::string& footer_url,const std::string& footer_text,const std::string& timestamp);	

		static void game__RoleLogout(int roleid, bool cross);
		static void game__RoleLogin(int roleid);

		static std::string game__GetUserIp(int roleid);
		static unsigned long long game__GetHwid(int roleid);
	public:	
		time_t GetFileTime(const char *path);
		bool GetNum(const char* s, double& v);
		bool GetString(const char* s, const char* v);
		bool SetNum(const char* s, double v);
		bool SetString(const char* s, const char* v);
		bool SetAddr(const char* s, long long v);
		bool IsTrue(int it, int * table);
		
		void FunctionsRegister();
		void FunctionsExec();
		void SetItem();
		void GetItem();
		void Init();
		void Update();
		void HeartBeat();
		
		time_t BidBeginTime(time_t now);
		time_t BidEndTime(time_t now);
		time_t BattleTime(time_t now);
		time_t RewardTime(time_t now);
		time_t BattleInterval(size_t num);
		
		time_t CountryBattleStartTime();
		time_t CountryBattleBonusTime();
		time_t CountryBattleClearTime();
		size_t CountryMaxCount();
		size_t CountryBattleBonus();
		int    CountryBattleItem();

		// Custom Arena 180
		time_t ArenaStartTime();
		time_t ArenaEndTime();
		time_t ArenaClearTime();

		void ArenaGetRoleName(int roleid, Octets & name);
		void ArenaTimerCounter(int roleid, int tick);
		void ArenaTeamTimerCounter(int roleid, int tick);
		void ArenaTimerCooldown(int roleid, int tick);
		void ArenaPlayerMessage(int roleid, int result);
		// End Custom Arena 180
		
		int EventOnEnterServer(int roleid, Octets & hwid, int & gameid);
		int EventOnSwitchServer(int roleid, int tag);
		int EventOnPlayerLuaInfo(int roleid, Octets & info);

		void sendArena1x1EndDiscordWebHook(int id_red, int id_blue, int result, int tick);
		void sendArena3x3EndDiscordWebHook(int id_red_1, int id_red_2, int id_red_3, int id_blue_1, int id_blue_2, int id_blue_3, int result, int tick);
		void sendArena6x6EndDiscordWebHook(int id_red_1, int id_red_2, int id_red_3, int id_red_4, int id_red_5, int id_red_6, int id_blue_1, int id_blue_2, int id_blue_3, int id_blue_4, int id_blue_5, int id_blue_6, int result, int tick);

	public:
		CONFIG * GetConfig() { return &config; }


		static LuaManager * GetInstance()
		{
			if (!instance)
			instance = new LuaManager();
			return instance;
		}
		static LuaManager * instance;
	};

	class LuaTimer : public Thread::Runnable
	{
		int update_time;
	public:
		LuaTimer(int _time,int _proir=1) : Runnable(_proir),update_time(_time) { }
		void Run();
	private:
		void UpdateTimer();
	};

};

#endif