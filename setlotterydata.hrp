
#ifndef __GNET_SETLOTTERYDATA_HPP
#define __GNET_SETLOTTERYDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "setlotterydataarg"
#include "setlotterydatares"

namespace GNET
{

class SetLotteryData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "setlotterydata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// SetLotteryDataArg *arg = (SetLotteryDataArg *)argument;
		// SetLotteryDataRes *res = (SetLotteryDataRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// SetLotteryDataArg *arg = (SetLotteryDataArg *)argument;
		// SetLotteryDataRes *res = (SetLotteryDataRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
