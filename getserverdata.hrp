
#ifndef __GNET_GETSERVERDATA_HPP
#define __GNET_GETSERVERDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "getserverdataarg"
#include "getserverdatares"

namespace GNET
{

class GetServerData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getserverdata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// GetServerDataArg *arg = (GetServerDataArg *)argument;
		// GetServerDataRes *res = (GetServerDataRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetServerDataArg *arg = (GetServerDataArg *)argument;
		// GetServerDataRes *res = (GetServerDataRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
