
#ifndef __GNET_ARENABATTLEEND_HPP
#define __GNET_ARENABATTLEEND_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#include "arenamanager.hpp"

namespace GNET
{

class ArenaBattleEnd : public GNET::Protocol
{
	#include "arenabattleend"

	void Process(Manager *manager, Manager::Session::ID sid)
	{
		///ArenaManager::GetInstance()->BattleEnd(id, mode, red_win, score);
	}
};

};

#endif
