
#ifndef __GNET_DBKEKINGCONFIRM_HPP
#define __GNET_DBKEKINGCONFIRM_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbkekingconfirmarg"
#include "dbkekingconfirmres"

#include "kingelection.h"
#include "gamedbclient.hpp"
#include "gproviderserver.hpp"
#include "kekingnotify.hpp"

namespace GNET
{

class DBKEKingConfirm : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbkekingconfirm"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBKEKingConfirmArg *arg = (DBKEKingConfirmArg *)argument;
		// DBKEKingConfirmRes *res = (DBKEKingConfirmRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		DBKEKingConfirmArg *arg = (DBKEKingConfirmArg *)argument;
		DBKEKingConfirmRes *res = (DBKEKingConfirmRes *)result;
		DEBUG_PRINT("dbkekingconfirm: rpc return. retcode=%d", res->retcode);
		if(res->retcode == ERR_SUCCESS)
		{
			Thread::RWLock::RDScoped l(UserContainer::GetInstance().GetLocker());
			PlayerInfo * pinfo = UserContainer::GetInstance().FindRoleOnline(arg->king.roleid);
			if(pinfo)
			{
				GProviderServer::GetInstance()->DispatchProtocol(pinfo->gameid, KEKingNotify(arg->king.roleid, arg->king.end_time));	
			}
		}
	}

	void OnTimeout()
	{
		// TODO Client Only
		DBKEKingConfirmArg *arg = (DBKEKingConfirmArg *)argument;
		Log::log(LOG_ERR,"dbkekingconfirm: timeout.\n");
		GameDBClient::GetInstance()->SendProtocol( Rpc::Call(RPC_DBKEKINGCONFIRM,arg) );
	}

};

};
#endif
