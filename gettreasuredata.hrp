
#ifndef __GNET_GETTREASUREDATA_HPP
#define __GNET_GETTREASUREDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "gettreasuredataarg"
#include "gettreasuredatares"

namespace GNET
{

class GetTreasureData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "gettreasuredata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// GetTreasureDataArg *arg = (GetTreasureDataArg *)argument;
		// GetTreasureDataRes *res = (GetTreasureDataRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// GetTreasureDataArg *arg = (GetTreasureDataArg *)argument;
		// GetTreasureDataRes *res = (GetTreasureDataRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
