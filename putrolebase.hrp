
#ifndef __GNET_PUTROLEBASE_HPP
#define __GNET_PUTROLEBASE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "rolebasepair"


namespace GNET
{

class PutRoleBase : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "putrolebase"
#undef	RPC_BASECLASS
	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_BDB
		RoleBasePair *arg = (RoleBasePair *)argument;
		RpcRetcode *res = (RpcRetcode *)result;
		Marshal::OctetsStream key, value;
		key << arg->key;
		value << arg->value;
		res->retcode = DBBuffer::buf_insert( "base", key, value );
#endif
	}
	void ResendRequest( RoleBasePair *arg )
	{
		GameDBClient::GetInstance()->SendProtocol(
				Rpc::Call(RPC_PUTROLEBASE,arg)
			);
	}
	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		 RoleBasePair *arg = (RoleBasePair *)argument;
		 RpcRetcode *res = (RpcRetcode *)result;
		 if (res->retcode != ERR_SUCCESS)
		 {
			 Log::log(LOG_ERR,"gdelivery: Putrolebase failed. roleid=%d, retcode=%d\n",arg->key.id,res->retcode);
			 if (res->retcode==ERR_AGAIN)
				 ResendRequest(arg);
		 }
	}

	void OnTimeout(Rpc::Data *argument)
	{
		// TODO Client Only
		RoleBasePair *arg = (RoleBasePair *)argument;
		Log::log(LOG_ERR,"gdelivery: Putrolebase failed(timeout). roleid=%d\n",arg->key.id);
		ResendRequest(arg);
	}

};

};
#endif
