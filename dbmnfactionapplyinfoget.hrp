
#ifndef __GNET_DBMNFACTIONAPPLYINFOGET_HPP
#define __GNET_DBMNFACTIONAPPLYINFOGET_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbmnfactionapplyinfogetarg"
#include "dbmnfactionapplyinfogetres"
#include "cdcmnfbattleman.h"

namespace GNET
{

class DBMNFactionApplyInfoGet : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbmnfactionapplyinfoget"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBMNFactionApplyInfoGetArg *arg = (DBMNFactionApplyInfoGetArg *)argument;
		// DBMNFactionApplyInfoGetRes *res = (DBMNFactionApplyInfoGetRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		DEBUG_PRINT("file=%s\tfunc=%s\tline=%d\n", __FILE__, __FUNCTION__, __LINE__);
		//DBMNFactionApplyInfoGetArg *arg = (DBMNFactionApplyInfoGetArg *)argument;
		DBMNFactionApplyInfoGetRes *res = (DBMNFactionApplyInfoGetRes *)result;

		if(res->retcode == ERR_SUCCESS)
		{
			bool is_central = GDeliveryServer::GetInstance()->IsCentralDS();
			if(is_central)
			{
				//CDS_MNFactionBattleMan::GetInstance()->OnGetMNFApplyInfoList(res->applyinfo_list);  
			}
			else
			{
				CDC_MNFactionBattleMan::GetInstance()->OnGetMNFApplyInfoList(res->applyinfo_list);  
			}
		} 
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
