[LogclientClient]
type            =   udp
port            =   11100
address         =   **********
so_sndbuf       =   ********
so_rcvbuf       =   16384
accumulate      =   ********

[LogclientTcpClient]
type            =   tcp
port            =   11101
address         =   **********
so_sndbuf       =   ********
so_rcvbuf       =   16384
accumulate      =   ********

[Intervals]
;set accounting interval,unit is second
account_interval    =   600
;set delrole interval,unit is second
delrole_interval    =   60
;set check forbidlogin user's map interval,unit is second
checkforbidlogin_interval = 60

[GDeliveryServer]
zoneid			=	1
aid			=	10
freeaid 		=	0
zondname		=	zone1
battlefield             =       1
table_charset           =       UCS2
table_name              =       filters
sellpoint		=	1
#freecreatime		=	-1
freecreatime		=	**********
challenge_algo		=	1	

type			=	tcp
port			=	29100
address			=	0.0.0.0
so_sndbuf		=	16384
so_rcvbuf		=	16384
ibuffermax              =       1048576
obuffermax              =       1048576
tcp_nodelay		=	0
listen_backlog	=	10
accumulate		=	1048576
mtrace			=	/tmp/m_trace.link

[GAuthClient]
type			=	tcp
port			=	29200
address			=	************
;address			=	***********
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast	=	1
tcp_nodelay		=	0
accumulate		=	*********
isec                    =       2
iseckey                 =       n1hxpxztozyxnsvk6RaycpmrCnrdds
osec                    =       2
oseckey                 =       rdppjtaki1MxoHnsnaltiiwfjszs9l
shared_key              =       4khdwAAcjrg0eqfzazqcemdpgulnje

[GAntiCheatClient]
type            =   tcp
port            =   29709
address         =   **********
so_sndbuf       =   16384
so_rcvbuf       =   16384
ibuffermax      =   1638400
obuffermax      =   1638400
;so_broadcast   =   1  
tcp_nodelay     =   0  
accumulate      =   *********
 
[GProviderServer]
id				=	0
type			=	tcp
port			=	29300
address			=	0.0.0.0
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast		=	1
tcp_nodelay		=	0
accumulate		=	*********

[GameDBClient]
type			=	tcp
port			=	29400
address			=	127.0.0.1
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast		=	1
tcp_nodelay		=	0
accumulate		=	*********
;isec			=	2
;iseckey			=	123
;osec			=	2
;oseckey			=	456

[UniqueNameClient]
type                    =       tcp
port                    =       29401
address                 =       127.0.0.1
so_sndbuf               =       16384
so_rcvbuf               =       16384
;so_broadcast           =       1
tcp_nodelay             =       0
accumulate              =       *********

[GFactionClient]
type                    =       tcp
port                    =       29500
address                 =       127.0.0.1
so_sndbuf               =       16384
so_rcvbuf               =       16384
ibuffermax              =       1638400
obuffermax              =       1638400
;so_broadcast           =       1
tcp_nodelay             =       0
accumulate              =       *********

[GRoleDBClient]
type			=	tcp
port			=	15000
address			=	127.0.0.1
so_sndbuf		=	16384
so_rcvbuf		=	16384
;so_broadcast		=	1
tcp_nodelay		=	0
accumulate		=	*********
;isec			=	2
;iseckey			=	123
;osec			=	2
;oseckey			=	456

[ThreadPool]
threads			=	(1,3)(100,1)(101,1)(0,1)
max_queuesize		=	1048576
prior_strict		=	1

[ConsumeReward]
open		=	1
begin_time	=	2009-8-10-8
end_time	=	2010-8-10-8
reward_type	=	(99,20)(199,50)(499,150)(999,350)
reward_time	=	(30,5)
