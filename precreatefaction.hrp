
#ifndef __GNET_PRECREATEFACTION_HPP
#define __GNET_PRECREATEFACTION_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "precreatefactionarg"
#include "precreatefactionres"

namespace GNET
{

class PreCreateFaction : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "precreatefaction"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_BDB
		PreCreateFactionArg *arg = (PreCreateFactionArg *)argument;
		PreCreateFactionRes *res = (PreCreateFactionRes *)result;
		Marshal::OctetsStream key, value;
		key << *arg;
		res->retcode = DBBuffer::buf_find( "unamefaction", key, value );
		if( 0 == res->retcode )
			value >> res->factionid;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// PreCreateFactionArg *arg = (PreCreateFactionArg *)argument;
		// PreCreateFactionRes *res = (PreCreateFactionRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
