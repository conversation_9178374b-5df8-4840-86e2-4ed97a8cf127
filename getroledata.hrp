
#ifndef __GNET_GETROLEDATA_HPP
#define __GNET_GETROLEDATA_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "roleid"
#include "roledatares"

namespace GNET
{

class GetRoleData : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "getroledata"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// RoleId *arg = (RoleId *)argument;
		// RoleDataRes *res = (RoleDataRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// RoleId *arg = (RoleId *)argument;
		// RoleDataRes *res = (RoleDataRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
