#ifndef __GNET_DBVERIFYMASTER_HPP
#define __GNET_DBVERIFYMASTER_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbverifymasterarg"
#include "deffactionres"
#include "verifymaster_re.hpp"

namespace GNET
{

class DBVerifyMaster : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbverifymaster"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBVerifyMasterArg *arg = (DBVerifyMasterArg *)argument;
		// DefFactionRes *res = (DefFactionRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		DBVerifyMasterArg *arg = (DBVerifyMasterArg *)argument;
		DefFactionRes *res = (DefFactionRes *)result;
		GAuthClient::GetInstance()->SendProtocol(VerifyMaster_Re(arg->name, res->retcode));
		
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
