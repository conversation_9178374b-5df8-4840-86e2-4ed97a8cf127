
#ifndef __GNET_EC_DBARENAPLAYERTOPLIST_HPP
#define __GNET_EC_DBARENAPLAYERTOPLIST_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "ec_dbarenaplayertoplistarg"
#include "ec_dbarenaplayertoplistres"

#include "ec_arenamanager.h"

namespace GNET
{

class EC_DBArenaPlayerTopList : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "ec_dbarenaplayertoplist"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// EC_DBArenaPlayerTopListArg *arg = (EC_DBArenaPlayerTopListArg *)argument;
		// EC_DBArenaPlayerTopListRes *res = (EC_DBArenaPlayerTopListRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		EC_DBArenaPlayerTopListArg *arg = (EC_DBArenaPlayerTopListArg *)argument;
		EC_DBArenaPlayerTopListRes *res = (EC_DBArenaPlayerTopListRes *)result;
		
		if (res->retcode == ERR_SUCCESS)
		{
			ArenaOfAuroraManager::GetInstance()->EC_SetArenaPlayerTopList(res->members);
		}
	}

	void OnTimeout()
	{
		Log::log(LOG_ERR,"EC_DBArenaPlayerTopList: timeout.\n");
	}

};

};
#endif
