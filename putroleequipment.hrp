
#ifndef __GNET_PUTROLEEQUIPMENT_HPP
#define __GNET_PUTROLEEQUIPMENT_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "roleequipmentpair"


namespace GNET
{

class PutRoleEquipment : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "putroleequipment"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// RoleEquipmentPair *arg = (RoleEquipmentPair *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// RoleEquipmentPair *arg = (RoleEquipmentPair *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
