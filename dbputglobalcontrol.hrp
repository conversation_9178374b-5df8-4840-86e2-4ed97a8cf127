
#ifndef __GNET_DBPUTGLOBALCONTROL_HPP
#define __GNET_DBPUTGLOBALCONTROL_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbputglobalcontrolarg"
#include "dbputglobalcontrolres"

#include "globalcontrol.h"
#include "gamedbclient.hpp"

namespace GNET
{

class DBPutGlobalControl : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbputglobalcontrol"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBPutGlobalControlArg *arg = (DBPutGlobalControlArg *)argument;
		// DBPutGlobalControlRes *res = (DBPutGlobalControlRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// DBPutGlobalControlArg *arg = (DBPutGlobalControlArg *)argument;
		DBPutGlobalControlRes *res = (DBPutGlobalControlRes *)result;
		DEBUG_PRINT("dbputglobalcontrol: rpc return. retcode=%d", res->retcode);
	}

	void OnTimeout()
	{
		// TODO Client Only
		DBPutGlobalControlArg *arg = (DBPutGlobalControlArg *)argument;
		Log::log(LOG_ERR,"dbputglobalcontrol: timeout.\n");
		GameDBClient::GetInstance()->SendProtocol( Rpc::Call(RPC_DBPUTGLOBALCONTROL,arg) );
	}

};

};
#endif
