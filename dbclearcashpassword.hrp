
#ifndef __GNET_DBCLEARCASHPASSWORD_HPP
#define __GNET_DBCLEARCASHPASSWORD_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbclearcashpasswordarg"
#include "dbclearcashpasswordres"

namespace GNET
{

class DBClearCashPassword : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbclearcashpassword"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBClearCashPasswordArg *arg = (DBClearCashPasswordArg *)argument;
		// DBClearCashPasswordRes *res = (DBClearCashPasswordRes *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// DBClearCashPasswordArg *arg = (DBClearCashPasswordArg *)argument;
		// DBClearCashPasswordRes *res = (DBClearCashPasswordRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
