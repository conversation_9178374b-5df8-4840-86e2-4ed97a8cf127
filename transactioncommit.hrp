
#ifndef __GNET_TRANSACTIONCOMMIT_HPP
#define __GNET_TRANSACTIONCOMMIT_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "transactionid"


namespace GNET
{

class TransactionCommit : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "transactioncommit"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TransactionId *arg = (TransactionId *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// TransactionId *arg = (TransactionId *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
