
#ifndef __GNET_DBREFWITHDRAWTRANS_HPP
#define __GNET_DBREFWITHDRAWTRANS_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "dbrefwithdrawtransarg"
#include "referencemanager.h"

namespace GNET
{

class DBRefWithdrawTrans : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbrefwithdrawtrans"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// DBRefWithdrawTransArg *arg = (DBRefWithdrawTransArg *)argument;
		// RpcRetcode *res = (RpcRetcode *)result;
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		DBRefWithdrawTransArg *arg = (DBRefWithdrawTransArg *)argument;
		RpcRetcode *res = (RpcRetcode *)result;

		ReferenceManager *refman = ReferenceManager::GetInstance();
		if (res->retcode == ERR_SUCCESS)
			refman->OnDBWithdrawConfirm(arg->referrer.userid);
		else
			refman->OnDBWithdrawRollback(arg->referrer.userid);
	}

	void OnTimeout(Rpc::Data *argument)
	{
		DBRefWithdrawTransArg *arg = (DBRefWithdrawTransArg *)argument;
		ReferenceManager::GetInstance()->OnDBWithdrawRollback(arg->referrer.userid);
	}

};

};
#endif
