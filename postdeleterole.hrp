
#ifndef __GNET_POSTDELETEROLE_HPP
#define __GNET_POSTDELETEROLE_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#ifdef USE_BDB
#include "dbbuffer.h"
#endif
#include "postdeleterolearg"
#include "postdeleteroleres"

namespace GNET
{

class PostDeleteRole : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "postdeleterole"
#undef	RPC_BASECLASS

	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
#ifdef USE_BDB
		PostDeleteRoleArg *arg = (PostDeleteRoleArg *)argument;
		PostDeleteRoleRes *res = (PostDeleteRoleRes *)result;
		Marshal::OctetsStream key, value;
		key << arg->rolename;
		value << arg->roleid;
#endif
	}

	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// TODO
		// PostDeleteRoleArg *arg = (PostDeleteRoleArg *)argument;
		// PostDeleteRoleRes *res = (PostDeleteRoleRes *)result;
	}

	void OnTimeout()
	{
		// TODO Client Only
	}

};

};
#endif
