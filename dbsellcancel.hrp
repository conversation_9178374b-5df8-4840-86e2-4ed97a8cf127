
#ifndef __GNET_DBSELLCANCEL_HPP
#define __GNET_DBSELLCANCEL_HPP

#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"

#include "sellid"
#include "dbsyncsellinfores"
namespace GNET
{

class DBSellCancel : public Rpc
{
#define	RPC_BASECLASS	Rpc
	#include "dbsellcancel"
#undef	RPC_BASECLASS
	void Server(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
		// SellID *arg = (SellID *)argument;
		// DBSyncSellInfoRes *res = (DBSyncSellInfoRes *)result;
	}
	void Client(Rpc::Data *argument, Rpc::Data *result, Manager *manager, Manager::Session::ID sid)
	{
	}

	void OnTimeout(Rpc::Data *argument)
	{
	}

};

};
#endif
