//------------------------------------------------------------------------------------------------------------------------
//--ARENA AURURE (C) 2022 AnotherCompiler
//------------------------------------------------------------------------------------------------------------------------
#include <algorithm>
#include <vector>
#include <pthread.h>
#include <iostream>
#include "rpcdefs.h"
#include "callid.hxx"
#include "state.hxx"
#include "maperaser.h"
#include "mapremaintime.h"
#include "mapfeeleak.h"
#include "mapuser.h"
#include "gamedbclient.hpp"
#include "gdeliveryserver.hpp"
#include "gproviderserver.hpp"
#include "arenabattlejoin.hpp"
#include "arenabattlequit.hpp"
#include "arenabattleresult.hpp"
#include "arenabattlestart.hpp"
#include "celestialbattlemanager.hpp"
#include "luaman.hpp"
#include <random>
#include <unordered_map>

namespace GNET
{
	CelestialBattleTimerManager* CelestialBattleTimerManager::instance = NULL;
	std::vector <CelestialBattleTimerManager::ROLEDATA> CelestialBattleTimerManager::roledata;
	int CelestialBattleTimerManager::tick;
	time_t CelestialBattleTimerManager::last_message_time_1x1_1;
	time_t CelestialBattleTimerManager::last_message_time_1x1_2;
	time_t CelestialBattleTimerManager::last_message_time_1x1_3;
	time_t CelestialBattleTimerManager::last_message_time_3x3_1;
	time_t CelestialBattleTimerManager::last_message_time_3x3_2;
	time_t CelestialBattleTimerManager::last_message_time_3x3_3;
	time_t CelestialBattleTimerManager::last_message_time_6x6_1;
	time_t CelestialBattleTimerManager::last_message_time_6x6_2;
	time_t CelestialBattleTimerManager::last_message_time_6x6_3;

	
	CelestialBattleManager* CelestialBattleManager::instance = NULL;
	bool CelestialBattleManager::log;
	time_t CelestialBattleManager::tick;
	pthread_mutex_t CelestialBattleManager::arena_mutex;
	std::vector<CelestialBattleManager::TEAMDATA> CelestialBattleManager::teamdata; 
	
	std::vector<int> CelestialBattleManager::v1x1;
	std::vector<int> CelestialBattleManager::v3x3;
	std::vector<int> CelestialBattleManager::v6x6;

	std::vector<int> CelestialBattleManager::random_team_3x3;
	std::vector<int> CelestialBattleManager::random_role_3x3;

	std::vector<int> CelestialBattleManager::random_team_6x6;
	std::vector<int> CelestialBattleManager::random_role_6x6;

	std::unordered_map<int, int> CelestialBattleManager::random_role_3x3_ticks;
	std::unordered_map<int, int> CelestialBattleManager::random_role_6x6_ticks;

	int CelestialBattleManager::team_counter;

	// Static thread function for BattleRelease
	void* CelestialBattleManager::ProcessTeamBattleRelease(void* arg) {
    	BattleReleaseThreadData* d = static_cast<BattleReleaseThreadData*>(arg);

    int id = d->arena_manager->GetTeam(d->team_id);
    if (id >= 0 && static_cast<size_t>(id) < d->arena_manager->teamdata.size() &&
        d->arena_manager->teamdata.at(id).COUNT_ROLE() &&
        d->arena_manager->teamdata.at(id).mode == d->mode &&
        d->arena_manager->teamdata.at(id).state == STATE_JOIN)
    {
        auto &t = d->arena_manager->teamdata.at(id);
        t.battle_id = d->battle_idx;
        t.tick      = d->lua->GetConfig()->ARENA_BATTLE_TIME;
        t.state     = STATE_BATTLE;
        t.red       = d->is_red_team ? FLAG_RED : FLAG_BLUE;

        for (unsigned i = 0; i < t.COUNT_ROLE() && i < t.GET_MAX_PLAYERS(); ++i) {
            PlayerInfo *pInfo = UserContainer::GetInstance().FindRoleOnline(t.GET_ROLE(i));
            if (pInfo && pInfo->user->gameid == 1) {
                d->lua->ArenaPlayerMessage(
                    pInfo->user->roleid,
                    d->is_red_team ? MSG_START_USER_RED : MSG_START_USER_BLUE
                );
                // ⚠️ protect abs if accessed concurrently
                // pthread_mutex_lock(&d->abs->mutex);
                (d->is_red_team ? d->abs->src : d->abs->dst).push_back(pInfo->user->roleid);
                // pthread_mutex_unlock(&d->abs->mutex);
            }
        }
    }

    	return nullptr;
	}

	void CelestialBattleTimerManager::Init()
	{
		roledata.clear();
		roledata.reserve(MAX_ROLE_TIMER);
		tick = 0;
		last_message_time_1x1_1 = 0;
		last_message_time_1x1_2 = 0;
		last_message_time_1x1_3 = 0;
		last_message_time_3x3_1 = 0;
		last_message_time_3x3_2 = 0;
		last_message_time_3x3_3 = 0;
		last_message_time_6x6_1 = 0;
		last_message_time_6x6_2 = 0;
		last_message_time_6x6_3 = 0;

	}

	void CelestialBattleTimerManager::HeartBeat()
	{

		roledata.erase(std::remove_if(roledata.begin(), roledata.end(), [](ROLEDATA &r) {
                           return r.tick-- < 1;
                       }),
                       roledata.end());

		if(!(tick++ % LuaManager::GetInstance()->GetConfig()->ARENA_CHECK_OPEN_TIME))
		{
			this->SendEnableMessage();
		}
	}

	bool CelestialBattleTimerManager::CheckEnable(int roleid, int mode)
	{
		time_t now = time(NULL);
		struct tm *timeinfo = localtime(&now);
		int dayofweek = timeinfo->tm_wday;
		now = timeinfo->tm_hour*HOUR + timeinfo->tm_min*MIN + timeinfo->tm_sec;
		//printf("CheckEnable: roleid = %d , mode = %d , now = %lld , dayofweek = %d \n", roleid, mode, now, dayofweek);
		switch(mode)
		{
			case CelestialBattleManager::MODE_1X1:
			{
				//printf("CheckEnable: roleid = %d , dayofweek = %d, enabled = %d  \n", roleid, dayofweek, (LuaManager::GetInstance()->GetConfig()->IS_TRUE_ARENA_WEEK(mode, dayofweek) ? 1 : 0));
				if(LuaManager::GetInstance()->GetConfig()->IS_TRUE_ARENA_WEEK(mode, dayofweek))
				{
					//printf("CheckEnable: roleid = %d , now = %lld, time_start_1 = %lld  \n", roleid, now, LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_1);
					//printf("CheckEnable: roleid = %d , now = %lld, time_end_1 = %lld  \n", roleid, now, LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_1);
					//printf("CheckEnable: roleid = %d , now = %lld, time_start_2 = %lld  \n", roleid, now, LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_2);
					//printf("CheckEnable: roleid = %d , now = %lld, time_end_2 = %lld  \n", roleid, now, LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_2);
					//printf("CheckEnable: roleid = %d , now = %lld, time_start_3 = %lld  \n", roleid, now, LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_3);
					//printf("CheckEnable: roleid = %d , now = %lld, time_end_3 = %lld  \n", roleid, now, LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_3);

					if (now > LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_1 && now < LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_1)
					{
						return true;
					}
					else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_2 && now < LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_2)
					{
						return true;
					}
					else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_3 && now < LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_3)
					{
						return true;
					}
					else
					{
						LuaManager::GetInstance()->ArenaPlayerMessage(roleid, CelestialBattleManager::MSG_TIME_DISTABLE_1X1);
						return false;
					}	
				}
			}
			break;

			case CelestialBattleManager::MODE_3X3:
			{
				if(LuaManager::GetInstance()->GetConfig()->IS_TRUE_ARENA_WEEK(mode, dayofweek))
				{
					if (now > LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_START_1 && now < LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_END_1)
					{
						return true;
					}
					else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_START_2 && now < LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_END_2)
					{
						return true;
					}
					else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_START_3 && now < LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_END_3)
					{
						return true;
					}
					else
					{
						LuaManager::GetInstance()->ArenaPlayerMessage(roleid, CelestialBattleManager::MSG_TIME_DISTABLE_3X3);
						return false;
					}	
				}
			}
			break;

			case CelestialBattleManager::MODE_6X6:
			{
				if(LuaManager::GetInstance()->GetConfig()->IS_TRUE_ARENA_WEEK(mode, dayofweek))
				{
					if (now > LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_START_1 && now < LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_END_1)
					{
						return true;
					}
					else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_START_2 && now < LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_END_2)
					{
						return true;
					}
					else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_START_3 && now < LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_END_3)
					{
						return true;
					}
					else
					{
						LuaManager::GetInstance()->ArenaPlayerMessage(roleid, CelestialBattleManager::MSG_TIME_DISTABLE_6X6);
						return false;
					}	
				}
			}
			break;

			default:
			{
				//printf("CelestialBattleTimerManager::CheckEnable: roleid = %d , now = %lld  \n", roleid, now);
				LuaManager::GetInstance()->ArenaPlayerMessage(roleid, CelestialBattleManager::MSG_TIME_DISTABLE);
				return false;
			}
		}

		LuaManager::GetInstance()->ArenaPlayerMessage(roleid, CelestialBattleManager::MSG_TIME_DISTABLE);
		return false;
	}

	void CelestialBattleTimerManager::SendEnableMessage()
	{
		time_t now = time(NULL);
		struct tm *timeinfo = localtime(&now);
		int dayofweek = timeinfo->tm_wday;
		now = timeinfo->tm_hour*HOUR + timeinfo->tm_min*MIN + timeinfo->tm_sec;

		const time_t message_interval = LuaManager::GetInstance()->GetConfig()->ARENA_MESSAGE_OPEN_INTERVAL;

		//printf("CheckEnable: roleid = %d , mode = %d , now = %lld , dayofweek = %d \n", roleid, mode, now, dayofweek);

		//printf("SendEnableMessage: now = %lld , dayofweek = %d, message_interval = %lld  \n", now, dayofweek, message_interval);
		//printf("SendEnableMessage: last_message_time_1x1_1 = %lld , last_message_time_1x1_2 = %lld , last_message_time_1x1_3 = %lld  \n", last_message_time_1x1_1, last_message_time_1x1_2, last_message_time_1x1_3);
		//printf("SendEnableMessage: last_message_time_3x3_1 = %lld , last_message_time_3x3_2 = %lld , last_message_time_3x3_3 = %lld  \n", last_message_time_3x3_1, last_message_time_3x3_2, last_message_time_3x3_3);
		//printf("SendEnableMessage: last_message_time_6x6_1 = %lld , last_message_time_6x6_2 = %lld , last_message_time_6x6_3 = %lld  \n", last_message_time_6x6_1, last_message_time_6x6_2, last_message_time_6x6_3);

		if(LuaManager::GetInstance()->GetConfig()->IS_TRUE_ARENA_WEEK(CelestialBattleManager::MODE_1X1, dayofweek))
		{
			if (now > LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_1 && now < LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_1)
			{
				if(difftime(now, last_message_time_1x1_1) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_1X1_OPEN);
					last_message_time_1x1_1 = now;
				}
			}
			else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_2 && now < LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_2)
			{
				if(difftime(now, last_message_time_1x1_2) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_1X1_OPEN);
					last_message_time_1x1_2 = now;
				}
			}
			else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_START_3 && now < LuaManager::GetInstance()->GetConfig()->ARENA_1X1_TIME_END_3)
			{
				if(difftime(now, last_message_time_1x1_3) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_1X1_OPEN);
					last_message_time_1x1_3 = now;
				}
			}
		}

		if(LuaManager::GetInstance()->GetConfig()->IS_TRUE_ARENA_WEEK(CelestialBattleManager::MODE_3X3, dayofweek))
		{
			if (now > LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_START_1 && now < LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_END_1)
			{
				if(difftime(now, last_message_time_3x3_1) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_3X3_OPEN);
					last_message_time_3x3_1 = now;
				}
			}
			else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_START_2 && now < LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_END_2)
			{
				if(difftime(now, last_message_time_3x3_2) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_3X3_OPEN);
					last_message_time_3x3_2 = now;
				}
			}
			else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_START_3 && now < LuaManager::GetInstance()->GetConfig()->ARENA_3X3_TIME_END_3)
			{
				if(difftime(now, last_message_time_3x3_3) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_3X3_OPEN);
					last_message_time_3x3_3 = now;
				}
			}
		}

		if(LuaManager::GetInstance()->GetConfig()->IS_TRUE_ARENA_WEEK(CelestialBattleManager::MODE_6X6, dayofweek))
		{
			if (now > LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_START_1 && now < LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_END_1)
			{
				if(difftime(now, last_message_time_6x6_1) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_6X6_OPEN);
					last_message_time_6x6_1 = now;
				}
			}
			else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_START_2 && now < LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_END_2)
			{
				if(difftime(now, last_message_time_6x6_2) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_6X6_OPEN);
					last_message_time_6x6_2 = now;
				}
			}
			else if (now > LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_START_3 && now < LuaManager::GetInstance()->GetConfig()->ARENA_6X6_TIME_END_3)
			{
				if(difftime(now, last_message_time_6x6_3) > message_interval)
				{
					LuaManager::GetInstance()->ArenaPlayerMessage(0, CelestialBattleManager::MSG_ARENA_6X6_OPEN);
					last_message_time_6x6_3 = now;
				}
			}
		}
		return;
	}

	void CelestialBattleTimerManager::AddRole(int roleid)
	{
		auto it = std::find_if(roledata.begin(), roledata.end(), [roleid](const ROLEDATA &r) {
            return r.roleid == roleid;
        });

        if (it != roledata.end())
        {
            roledata.erase(it);
        }

        roledata.emplace_back(roleid, LuaManager::GetInstance()->GetConfig()->ARENA_MATCH_COOLDOWN);
    }

	bool CelestialBattleTimerManager::CheckRole(int roleid)
	{
		auto it = std::find_if(roledata.begin(), roledata.end(), [roleid](const ROLEDATA &r) {
            return r.roleid == roleid;
        });

        if (it != roledata.end())
        {
            LuaManager::GetInstance()->ArenaTimerCooldown(roleid, it->tick);
            return false;
        }
        return true;
    }

	int CelestialBattleManager::GetTeam(int xid) 
    {
        auto it = std::find_if(teamdata.begin(), teamdata.end(), [xid](const TEAMDATA& team) {
            return team.id == xid;
        });

        int res = it != teamdata.end() ? std::distance(teamdata.begin(), it) : -1;
        if (this->log) printf("ARENA::GetTeam: xid = %d , res = %d \n", xid, res);
        return res;
    }

	unsigned int CelestialBattleManager::CountTeamJoin() 
    {
        auto count = std::count_if(teamdata.begin(), teamdata.end(), [](const TEAMDATA& team) {
            return team.state == STATE_JOIN;
        });
        if (this->log) printf("ARENA::CountTeamJoin: res = %u \n", count);
        return count;
    }

	unsigned int CelestialBattleManager::CountTeamBattle() 
    {
        auto count = std::count_if(teamdata.begin(), teamdata.end(), [](const TEAMDATA& team) {
            return team.state == STATE_BATTLE;
        });
        if (this->log) printf("ARENA::CountTeamBattle: res = %u \n", count);
        return count;
    }

	unsigned int CelestialBattleManager::CountTeamEnd() 
    {
        auto count = std::count_if(teamdata.begin(), teamdata.end(), [](const TEAMDATA& team) {
            return team.state == STATE_END;
        });
        if (this->log) printf("ARENA::CountTeamEnd: res = %u \n", count);
        return count;
    }

	int CelestialBattleManager::SearchRole(int roleid)
	{
		bool found = std::any_of(teamdata.begin(), teamdata.end(), [roleid](const TEAMDATA &team) {
			return team.CHECK_ROLE(roleid);
		});

		int res = found ? 1 : 0;

		if (this->log)
			printf("ARENA::SearchRole: roleid = %d , res = %d \n", roleid, res);
		return res;
	}
	
	int CelestialBattleManager::AddTeam(int xid, char mode, int score)
	{
		int res = 0; // 0 - not added, 1 added, 2 already exists
		pthread_mutex_lock(&arena_mutex);
		LuaManager *lua = LuaManager::GetInstance();

		auto it = std::find_if(teamdata.begin(), teamdata.end(), [xid](const TEAMDATA& team) {
            return team.id == xid || team.CHECK_ROLE(xid);
        });

        if (it != teamdata.end())
        {
            res = 2;
        }
        else if (teamdata.size() < MAX_TEAM)
        {
			unsigned long long new_hwid = GetHwid(xid);
			bool hwid_exists = std::any_of(teamdata.begin(), teamdata.end(), [new_hwid, this](const TEAMDATA &team) {
				return std::any_of(team.role.begin(), team.role.end(), [new_hwid, this](int r_id) {
					return GetHwid(r_id) == new_hwid;
				});
			});

			if (hwid_exists && lua->GetConfig()->arena_hwid_check_enable)
			{
				res = 0; // HWID conflict
				LuaManager::GetInstance()->ArenaPlayerMessage(xid, MSG_FAIL_HWID); // Envia uma mensagem de falha ao jogador
			}
			else
			{
				teamdata.emplace_back(xid, mode, LuaManager::GetInstance()->GetConfig()->ARENA_WHITE_TIME, score);
           		res = 1;
			}  
        }

		pthread_mutex_unlock(&arena_mutex);
		if (this->log) printf("ARENA::AddTeam: xid = %d, mode = %d, res = %d\n", xid, mode, res);
		return res;
	}

	int CelestialBattleManager::DelTeam(int xid)
	{
		int res = 0; // 0 - does not exist, 1 is deleted
		pthread_mutex_lock(&arena_mutex);
		LuaManager *lua = LuaManager::GetInstance();

		auto it = std::find_if(teamdata.begin(), teamdata.end(), [xid](const TEAMDATA &team) {
			return team.id == xid && team.state == STATE_JOIN;
		});

		if (it != teamdata.end())
		{
			std::for_each(it->role.begin(), it->role.end(), [lua](int roleid) {
				if (roleid >= DEF_ROLEID)
					lua->ArenaPlayerMessage(roleid, MSG_DEL_USER_SUCCES);
			});
			it->CLEAR_ROLELIST();
			teamdata.erase(it);
			res = 1;
		}

		pthread_mutex_unlock(&arena_mutex);
		if (this->log) printf("ARENA::DelTeam: xid = %d, res = %d\n", xid, res);
		return res;
	}

	unsigned long long CelestialBattleManager::GetHwid(int roleid)
	{
		return LuaManager::GetInstance()->game__GetHwid(roleid);
	}

	int CelestialBattleManager::AddRole(int xid, int roleid)
	{
		int res = MSG_ADD_USER_FAIL; // 0 - not added, 1 added, 2 already exists, 3 - the player is participating in the battle
		pthread_mutex_lock(&arena_mutex);
		LuaManager *lua = LuaManager::GetInstance();

		auto it = std::find_if(teamdata.begin(), teamdata.end(), [roleid](const TEAMDATA &team) {
			return team.CHECK_ROLE(roleid);
		});

		if (it != teamdata.end())
		{
			res = (it->state == STATE_JOIN) ? MSG_ADD_USER_IS_JOIN : MSG_ADD_USER_IS_BATTLE;
		}
		else
		{
			unsigned long long new_hwid = GetHwid(roleid);
			bool hwid_exists = std::any_of(teamdata.begin(), teamdata.end(), [new_hwid, this](const TEAMDATA &team) {
				return std::any_of(team.role.begin(), team.role.end(), [new_hwid, this](int r_id) {
					return GetHwid(r_id) == new_hwid;
				});
			});

			if(hwid_exists && lua->GetConfig()->arena_hwid_check_enable)
			{
				res = MSG_FAIL_HWID;
			}
			else
			{
				it = std::find_if(teamdata.begin(), teamdata.end(), [xid](const TEAMDATA &team) {
					return team.id == xid && team.COUNT_ROLE() < team.GET_MAX_PLAYERS() && team.state == STATE_JOIN;
				});

				if (it != teamdata.end())
				{
					it->ADD_ROLE(roleid);
					res = MSG_ADD_USER_SUCCES;
				}
			}
		}

		if (roleid >= DEF_ROLEID)
			lua->ArenaPlayerMessage(roleid, res);
		
		pthread_mutex_unlock(&arena_mutex);
		if (this->log) printf("ARENA::AddRole: xid = %d, roleid = %d, res = %d\n", xid, roleid, res);
		return res;
	}

	int CelestialBattleManager::DelRole(int roleid)
	{
		int res = MSG_DEL_USER_FAIL; // 4 - does not exist, 5 - is deleted, 8 - player participates in the battle
		pthread_mutex_lock(&arena_mutex);
		LuaManager *lua = LuaManager::GetInstance();

		auto it = std::find_if(teamdata.begin(), teamdata.end(), [roleid](const TEAMDATA &team) {
			return team.CHECK_ROLE(roleid);
		});

		if (it != teamdata.end())
		{
			if (it->state == STATE_JOIN)
			{
				it->DEL_ROLE(roleid);
				res = MSG_DEL_USER_SUCCES;
			}
			else
			{
				res = MSG_DEL_USER_IS_BATTLE;
			}
		}
		else
		{
			auto random_it_3x3 = std::find(random_role_3x3.begin(), random_role_3x3.end(), roleid);
			if (random_it_3x3 != random_role_3x3.end())
			{
				random_role_3x3.erase(random_it_3x3);
				random_role_3x3_ticks.erase(roleid);
				res = MSG_DEL_USER_SUCCES;
			}

			auto random_it_6x6 = std::find(random_role_6x6.begin(), random_role_6x6.end(), roleid);
			if (random_it_6x6 != random_role_6x6.end())
			{
				random_role_6x6.erase(random_it_6x6);
				random_role_6x6_ticks.erase(roleid);
				res = MSG_DEL_USER_SUCCES;
			}
		}

		lua->ArenaPlayerMessage(roleid, res);
		pthread_mutex_unlock(&arena_mutex);
		if (this->log) printf("ARENA::DelRole: roleid = %d, res = %d\n", roleid, res);
		return res;
	}

	//-- Random Role 3x3
	int CelestialBattleManager::AddSoloRole3x3(int roleid)
    {
        int res = MSG_ADD_USER_FAIL; // 0 - not added, 1 added, 2 already exists, 3 - the player is participating in the battle
        pthread_mutex_lock(&arena_mutex);
        LuaManager *lua = LuaManager::GetInstance();

        if (SearchRole(roleid))
        {
            res = MSG_ADD_USER_IS_JOIN;
        }
        else if (std::find(random_role_3x3.begin(), random_role_3x3.end(), roleid) != random_role_3x3.end())
        {
            res = MSG_ADD_USER_IS_BATTLE;
        }
        else
        {
            unsigned long long new_hwid = GetHwid(roleid);
            bool hwid_exists = std::any_of(random_role_3x3.begin(), random_role_3x3.end(), [new_hwid, this](int r_id) {
                return GetHwid(r_id) == new_hwid;
            });

            if(hwid_exists && lua->GetConfig()->arena_hwid_check_enable)
            {
                res = MSG_FAIL_HWID;
            }
            else
            {
                random_role_3x3.push_back(roleid);
				random_role_3x3_ticks[roleid] = lua->GetConfig()->ARENA_SEARCH_TEAM_TIME;
                res = MSG_ADD_USER_SUCCES;

                if (this->log) printf("ARENA::AddSoloRole3x3: Added roleid = %d to random_role_3x3 queue. Total solo players = %d\n", roleid, (int)random_role_3x3.size());
            }
        }

        if (roleid >= DEF_ROLEID)
            lua->ArenaPlayerMessage(roleid, res);

        pthread_mutex_unlock(&arena_mutex);
        if (this->log)
            printf("ARENA::AddSoloRole3x3: roleid = %d, res = %d\n", roleid, res);
        return res;
    }

	int CelestialBattleManager::DelSoloRole3x3(int roleid)
    {
        int res = MSG_DEL_USER_FAIL; // 4 - does not exist, 5 - is deleted
        pthread_mutex_lock(&arena_mutex);
        LuaManager *lua = LuaManager::GetInstance();

        auto it = std::find(random_role_3x3.begin(), random_role_3x3.end(), roleid);
        if (it != random_role_3x3.end())
        {
            random_role_3x3.erase(it);
			random_role_3x3_ticks.erase(roleid);
            res = MSG_DEL_USER_SUCCES;
            if (this->log) printf("ARENA::DelSoloRole3x3: Removed roleid = %d from solo3x3 queue\n", roleid);
        }
        else
        {
            res = MSG_DEL_USER_FAIL;
        }

        if (roleid >= DEF_ROLEID)
            lua->ArenaPlayerMessage(roleid, res);

        pthread_mutex_unlock(&arena_mutex);
        if (this->log)
            printf("ARENA::DelSoloRole3x3: roleid = %d, res = %d\n", roleid, res);
        return res;
    }

	//-- Random Role 6x6

	int CelestialBattleManager::AddSoloRole6x6(int roleid)
    {
        int res = MSG_ADD_USER_FAIL; // 0 - not added, 1 added, 2 already exists, 3 - the player is participating in the battle
        pthread_mutex_lock(&arena_mutex);
        LuaManager *lua = LuaManager::GetInstance();

        if (SearchRole(roleid))
        {
            res = MSG_ADD_USER_IS_JOIN;
        }
        else if (std::find(random_role_6x6.begin(), random_role_6x6.end(), roleid) != random_role_6x6.end())
        {
            res = MSG_ADD_USER_IS_BATTLE;
        }
        else
        {
            unsigned long long new_hwid = GetHwid(roleid);
            bool hwid_exists = std::any_of(random_role_6x6.begin(), random_role_6x6.end(), [new_hwid, this](int r_id) {
                return GetHwid(r_id) == new_hwid;
            });

            if(hwid_exists && lua->GetConfig()->arena_hwid_check_enable)
            {
                res = MSG_FAIL_HWID;
            }
            else
            {
                random_role_6x6.push_back(roleid);
				random_role_6x6_ticks[roleid] = lua->GetConfig()->ARENA_SEARCH_TEAM_TIME;
                res = MSG_ADD_USER_SUCCES;

                if (this->log) printf("ARENA::AddSoloRole6x6: Added roleid = %d to random_role_6x6 queue. Total solo players = %d\n", roleid, (int)random_role_6x6.size());
            }
        }

        if (roleid >= DEF_ROLEID)
            lua->ArenaPlayerMessage(roleid, res);

        pthread_mutex_unlock(&arena_mutex);
        if (this->log)
            printf("ARENA::AddSoloRole6x6: roleid = %d, res = %d\n", roleid, res);
        return res;
    }

	int CelestialBattleManager::DelSoloRole6x6(int roleid)
    {
        int res = MSG_DEL_USER_FAIL; // 4 - does not exist, 5 - is deleted
        pthread_mutex_lock(&arena_mutex);
        LuaManager *lua = LuaManager::GetInstance();

        auto it = std::find(random_role_6x6.begin(), random_role_6x6.end(), roleid);
        if (it != random_role_6x6.end())
        {
            random_role_6x6.erase(it);
			random_role_6x6_ticks.erase(roleid);
            res = MSG_DEL_USER_SUCCES;
            if (this->log) printf("ARENA::DelSoloRole6x6: Removed roleid = %d from random_role_6x6 queue\n", roleid);
        }
        else
        {
            res = MSG_DEL_USER_FAIL;
        }

        if (roleid >= DEF_ROLEID)
            lua->ArenaPlayerMessage(roleid, res);

        pthread_mutex_unlock(&arena_mutex);
        if (this->log)
            printf("ARENA::DelSoloRole6x6: roleid = %d, res = %d\n", roleid, res);
        return res;
    }


	//-----------------------------------------------------
	std::vector<int> CelestialBattleManager::GetRedTeamRoles(int battle_id)
    {
        std::vector<int> redTeamRoles;
        pthread_mutex_lock(&arena_mutex);

        auto it = std::find_if(teamdata.begin(), teamdata.end(), [battle_id](const TEAMDATA& team) {
                return team.battle_id == battle_id && team.red == FLAG_RED;
            });

            if (it != teamdata.end())
            {
                for (unsigned int i = 0; i < it->COUNT_ROLE() && i < it->GET_MAX_PLAYERS(); i++)
                {
                    redTeamRoles.push_back(it->GET_ROLE(i));
                }
            }

        pthread_mutex_unlock(&arena_mutex);
        return redTeamRoles;
    }

    std::vector<int> CelestialBattleManager::GetBlueTeamRoles(int battle_id)
    {
        std::vector<int> blueTeamRoles;
        pthread_mutex_lock(&arena_mutex);

         auto it = std::find_if(teamdata.begin(), teamdata.end(), [battle_id](const TEAMDATA& team) {
                return team.battle_id == battle_id && team.red == FLAG_BLUE;
            });

            if (it != teamdata.end())
            {
                for (unsigned int i = 0; i < it->COUNT_ROLE() && i < it->GET_MAX_PLAYERS(); i++)
                {
                    blueTeamRoles.push_back(it->GET_ROLE(i));
                }
            }

        pthread_mutex_unlock(&arena_mutex);
        return blueTeamRoles;
    }

	void CelestialBattleManager::AutoClean()
	{
		pthread_mutex_lock(&arena_mutex);
		LuaManager *lua = LuaManager::GetInstance();

		for (auto &team : teamdata)
		{
			if (team.state == STATE_JOIN)
			{
				auto removeCondition = [lua](int roleid, const TEAMDATA &team) {
					if (roleid < DEF_ROLEID || team.tick <= 1)
					{
						if (roleid >= DEF_ROLEID)
							lua->ArenaPlayerMessage(roleid, MSG_DEL_USER_TIMEOUT);
						return true;
					}
					else
					{
						PlayerInfo *pUser = UserContainer::GetInstance().FindRoleOnline(roleid);
						if (!pUser || pUser->user->gameid != 1)
						{
							if (roleid >= DEF_ROLEID)
								lua->ArenaPlayerMessage(roleid, MSG_DEL_USER_TAG);
							return true;
						}
					}
					return false;
				};

				team.role.erase(std::remove_if(team.role.begin(), team.role.end(), [&team, &removeCondition](int roleid) {
					return removeCondition(roleid, team);
				}), team.role.end());
			}
		}

		teamdata.erase(std::remove_if(teamdata.begin(), teamdata.end(), [](const TEAMDATA &team) {
			return team.COUNT_ROLE() == 0 || team.tick <= 1;
		}), teamdata.end());


		// Clean up random_role_3x3 queue
		random_role_3x3.erase(std::remove_if(random_role_3x3.begin(), random_role_3x3.end(), [this, lua](int roleid) {
			PlayerInfo *pUser = UserContainer::GetInstance().FindRoleOnline(roleid);
			if (!pUser || pUser->user->gameid != 1)
			{
				if (roleid >= DEF_ROLEID)
					lua->ArenaPlayerMessage(roleid, MSG_DEL_USER_TAG);
				if (this->log) printf("ARENA::AutoClean: Removed roleid = %d from random_role_3x3 queue due to inactivity\n", roleid);
				return true;
			}
			return false;
		}), random_role_3x3.end());

		// Clean up random_role_6x6 queue
		random_role_6x6.erase(std::remove_if(random_role_6x6.begin(), random_role_6x6.end(), [this, lua](int roleid) {
			PlayerInfo *pUser = UserContainer::GetInstance().FindRoleOnline(roleid);
			if (!pUser || pUser->user->gameid != 1)
			{
				if (roleid >= DEF_ROLEID)
					lua->ArenaPlayerMessage(roleid, MSG_DEL_USER_TAG);
				if (this->log) printf("ARENA::AutoClean: Removed roleid = %d from random_role_3x3 queue due to inactivity\n", roleid);
				return true;
			}
			return false;
		}), random_role_6x6.end());

		pthread_mutex_unlock(&arena_mutex);
		if (this->log)
			printf("ARENA::AutoClean: Count = %d, CountJoin = %d, CountBattle = %d, CountEnd = %d\n",
				teamdata.size(), CountTeamJoin(), CountTeamBattle(), CountTeamEnd());
	}

	
	void CelestialBattleManager::SetPool()
	{
		pthread_mutex_lock(&arena_mutex);
		//std::sort(teamdata.begin(), teamdata.end(),TeamComp);
		
		v1x1.clear(); v1x1.reserve(MAX_TEAM);
		v3x3.clear(); v3x3.reserve(MAX_TEAM);
		v6x6.clear(); v6x6.reserve(MAX_TEAM);

		for (const auto& team : teamdata)
		{
			if (team.state == STATE_JOIN && !team.isRandomTeam)
			{
				switch (team.mode)
				{
					case MODE_1X1:
						v1x1.push_back(team.id);
						break;
					case MODE_3X3:
						v3x3.push_back(team.id);
						break;
					case MODE_6X6:
						v6x6.push_back(team.id);
						break;
					default:
						break;
				}
			}
		}

		// Remove teams with invalid modes or exceeding MAX_TEAM
		teamdata.erase(std::remove_if(teamdata.begin(), teamdata.end(), [](const TEAMDATA& team) {
			return team.mode != MODE_1X1 && team.mode != MODE_3X3 && team.mode != MODE_6X6;
		}), teamdata.end());

		std::random_device rd;
		std::mt19937 g(rd());
		std::shuffle(v1x1.begin(), v1x1.end(), g);
		std::shuffle(v3x3.begin(), v3x3.end(), g);
		std::shuffle(v6x6.begin(), v6x6.end(), g);
		
		pthread_mutex_unlock(&arena_mutex);
		if( this->log ) printf("ARENA::SetPool: 1x1Count = %d , 3x3Count = %d , 6x6Count = %d \n", v1x1.size() , v3x3.size() , v6x6.size() );
	}

	void CelestialBattleManager::RandomSetPool()
    {
        pthread_mutex_lock(&arena_mutex);

        random_team_3x3.clear(); random_team_3x3.reserve(MAX_TEAM);
		random_team_6x6.clear(); random_team_6x6.reserve(MAX_TEAM);

        // Handle random teams in teamdata
        for (const auto& team : teamdata)
		{
			if (team.state == STATE_JOIN && team.isRandomTeam)
			{
				switch (team.mode)
				{
					case MODE_3X3:
							random_team_3x3.push_back(team.id);
						break;
					case MODE_6X6:
							random_team_6x6.push_back(team.id);
						break;
					default:
						break;
				}
			}
		}

        // Shuffle the vector of random team IDs
        std::random_device rd;
        std::mt19937 g(rd());
        std::shuffle(random_team_3x3.begin(), random_team_3x3.end(), g);
		std::shuffle(random_team_6x6.begin(), random_team_6x6.end(), g);

        pthread_mutex_unlock(&arena_mutex);
        if( this->log ) printf("ARENA::RandomSetPool: random_team_3x3_count = %d, random_team_6x6_count = %d \n", (int)random_team_3x3.size(), (int)random_team_6x6.size());
    }

	void CelestialBattleManager::RandomCreateTeams()
	{
		pthread_mutex_lock(&arena_mutex);

		// Random 3x3 Mode
	if (random_role_3x3.size() >= 6)
	{
    	// Shuffle the roleids
    	std::random_device rd;
    	std::mt19937 g(rd());
    	std::shuffle(random_role_3x3.begin(), random_role_3x3.end(), g);

    	// Build cls_roles map
    	std::map<char, std::vector<int>> cls_roles;
    	for (auto roleid : random_role_3x3)
    	{
        	PlayerInfo* pInfo = UserContainer::GetInstance().FindRoleOnline(roleid);
        	if (pInfo == NULL)
            	continue;
        		char cls = pInfo->cls;
        		cls_roles[cls].push_back(roleid);
    	}

    // Check if we have at least 3 different classes to form teams
    // (can form 2 teams of 3 with different classes in each team)
    if (cls_roles.size() >= 3)
    {
        // Convert cls_roles map to a vector and shuffle
        std::vector<std::pair<char, std::vector<int>>> shuffled_cls_roles(cls_roles.begin(), cls_roles.end());
        std::shuffle(shuffled_cls_roles.begin(), shuffled_cls_roles.end(), g);

        // Try to form 2 teams
        std::vector<TEAMDATA> formed_teams;
        
        for (int team_idx = 0; team_idx < 2 && shuffled_cls_roles.size() >= 3; ++team_idx)
        {
            int new_team_id = ++team_counter;
            TEAMDATA new_team(new_team_id, MODE_3X3, LuaManager::GetInstance()->GetConfig()->ARENA_SEARCH_TEAM_TIME, 0, true);

            // Select 3 different classes for this team
            auto it = shuffled_cls_roles.begin();
            int roles_added = 0;
            
            while (roles_added < 3 && it != shuffled_cls_roles.end())
            {
                if (!it->second.empty())
                {
                    int roleid = it->second.back();
                    it->second.pop_back();
                    new_team.ADD_ROLE(roleid);
                    random_role_3x3_ticks.erase(roleid);
                    roles_added++;
                    
                    if (it->second.empty())
                        it = shuffled_cls_roles.erase(it);
                    else
                        ++it;
                }
                else
                {
                    it = shuffled_cls_roles.erase(it);
                }
            }

            if (new_team.COUNT_ROLE() == 3)
            {
                formed_teams.push_back(new_team);
            }
            else
            {
                // Return roles to queue if couldn't form full team
                for (int j = 0; j < new_team.COUNT_ROLE(); ++j)
                {
                    int role_id = new_team.GET_ROLE(j);
                    random_role_3x3_ticks[role_id] = LuaManager::GetInstance()->GetConfig()->ARENA_SEARCH_TEAM_TIME;
                }
                break;
            }
        }

        // Add successfully formed teams
        for (auto& new_team : formed_teams)
        {
            teamdata.push_back(new_team);
            
            // Send messages to the players
            for (int j = 0; j < 3; ++j)
            {
                int role_id = new_team.GET_ROLE(j);
                if (role_id >= DEF_ROLEID)
                    LuaManager::GetInstance()->ArenaPlayerMessage(role_id, MSG_ADD_USER_SUCCES);
                if (this->log) printf("ARENA::CreateRandom3x3Teams: Formed new random team with roleid = %d\n", role_id);
            }
        }

        // Collect remaining roles
        std::vector<int> remaining_roles;
        for (auto& pair : shuffled_cls_roles)
            remaining_roles.insert(remaining_roles.end(), pair.second.begin(), pair.second.end());

        random_role_3x3 = remaining_roles;
    }

		// // Random 3x3 Mode
		// if (random_role_3x3.size() >= 6)
		// {
		// 	// Shuffle the roleids
		// 	std::random_device rd;
		// 	std::mt19937 g(rd());
		// 	std::shuffle(random_role_3x3.begin(), random_role_3x3.end(), g);

		// 	// Build cls_roles map
		// 	std::map<char, std::vector<int>> cls_roles;
		// 	for (auto roleid : random_role_3x3)
		// 	{
		// 		PlayerInfo* pInfo = UserContainer::GetInstance().FindRoleOnline(roleid);
		// 		if (pInfo == NULL)
		// 			continue; // Skip if player info is not found
		// 		char cls = pInfo->cls;
		// 		cls_roles[cls].push_back(roleid);
		// 	}

		// 	// Check if at least 6 unique classes are available
		// 	if (cls_roles.size() >= 6)
		// 	{
		// 		// Convert cls_roles map to a vector and shuffle
		// 		std::vector<std::pair<char, std::vector<int>>> shuffled_cls_roles(cls_roles.begin(), cls_roles.end());
		// 		std::shuffle(shuffled_cls_roles.begin(), shuffled_cls_roles.end(), g);

		// 		for (int i = 0; i < 2; ++i) // Create two teams
		// 		{
		// 			int new_team_id = ++team_counter;
		// 			TEAMDATA new_team(new_team_id, MODE_3X3, LuaManager::GetInstance()->GetConfig()->ARENA_SEARCH_TEAM_TIME, 0, true);

		// 			// Select 3 different classes
		// 			auto it = shuffled_cls_roles.begin();
		// 			for (int j = 0; j < 3; ++j)
		// 			{
		// 				if (it == shuffled_cls_roles.end())
		// 					break;

		// 				char cls = it->first;
		// 				int roleid = it->second.back();
		// 				it->second.pop_back();
		// 				new_team.ADD_ROLE(roleid);
		// 				random_role_3x3_ticks.erase(roleid);

		// 				if (it->second.empty())
		// 					it = shuffled_cls_roles.erase(it);
		// 				else
		// 					++it;
		// 			}

		// 			if (new_team.COUNT_ROLE() == 3)
		// 			{
		// 				teamdata.push_back(new_team);

		// 				// Send messages to the players
		// 				for (int j = 0; j < 3; ++j)
		// 				{
		// 					int role_id = new_team.GET_ROLE(j);
		// 					if (role_id >= DEF_ROLEID)
		// 						LuaManager::GetInstance()->ArenaPlayerMessage(role_id, MSG_ADD_USER_SUCCES);
		// 					if (this->log) printf("ARENA::CreateRandom3x3Teams: Formed new random team with roleid = %d\n", role_id);
		// 				}
		// 			}
		// 		}

		// 		// Collect remaining roles
		// 		std::vector<int> remaining_roles;
		// 		for (auto& pair : shuffled_cls_roles)
		// 			remaining_roles.insert(remaining_roles.end(), pair.second.begin(), pair.second.end());

		// 		random_role_3x3 = remaining_roles;
		// 	}
		}

		// Random 6x6 Mode
		if (random_role_6x6.size() >= 12)
		{
			// Shuffle the roleids
			std::random_device rd;
			std::mt19937 g(rd());
			std::shuffle(random_role_6x6.begin(), random_role_6x6.end(), g);

			// Build cls_roles map
			std::map<char, std::vector<int>> cls_roles;
			for (auto roleid : random_role_6x6)
			{
				PlayerInfo* pInfo = UserContainer::GetInstance().FindRoleOnline(roleid);
				if (pInfo == NULL)
					continue; // Skip if player info is not found
				char cls = pInfo->cls;
				cls_roles[cls].push_back(roleid);
			}

			// Check if at least 12 unique classes are available based on combinations
			bool can_form_teams = false;
			int teams_formed = 0;
			std::vector<TEAMDATA> new_teams;
			std::map<char, std::vector<int>> original_cls_roles = cls_roles;

			while (teams_formed < 2)
			{
				bool team_formed = false;

				// Attempt combinations in order of preference
				std::vector<std::vector<int>> combinations = {
					{2, 2, 2},      // 3 classes, 2 roles each
					{2, 2, 1, 1},   // 4 classes, 2 roles for two classes, 1 role for two classes
					{2, 1, 1, 1, 1},// 5 classes, 2 roles for one class, 1 role for four classes
					{1, 1, 1, 1, 1, 1} // 6 classes, 1 role each
				};

				for (auto& combo : combinations)
				{
					// Shuffle the classes to pick from random classes
					std::vector<char> classes;
					for (auto& pair : cls_roles)
						classes.push_back(pair.first);
					std::shuffle(classes.begin(), classes.end(), g);

					std::vector<int> team_roles;
					std::map<char, int> cls_counts;
					bool valid_combination = true;

					size_t combo_index = 0;
					for (auto cls : classes)
					{
						auto& roles = cls_roles[cls];
						int count_needed = combo[combo_index];
						if (roles.size() < count_needed)
						{
							valid_combination = false;
							break;
						}

						for (int i = 0; i < count_needed; ++i)
						{
							team_roles.push_back(roles.back());
							roles.pop_back();
						}
						if (roles.empty())
							cls_roles.erase(cls);

						cls_counts[cls] += count_needed;

						combo_index++;
						if (combo_index >= combo.size())
							break;
					}

					if (valid_combination && team_roles.size() == 6)
					{
						int new_team_id = ++team_counter;
						TEAMDATA new_team(new_team_id, MODE_6X6, LuaManager::GetInstance()->GetConfig()->ARENA_SEARCH_TEAM_TIME, 0, true);

						for (auto roleid : team_roles)
						{
							new_team.ADD_ROLE(roleid);
							random_role_6x6_ticks.erase(roleid);
						}
							
						new_teams.push_back(new_team);
						teams_formed++;
						team_formed = true;
						break;
					}
					else
					{
						// Return roles to the cls_roles map
						for (auto roleid : team_roles)
						{
							PlayerInfo* pInfo = UserContainer::GetInstance().FindRoleOnline(roleid);
							if (pInfo)
								cls_roles[pInfo->cls].push_back(roleid);
						}
					}
				}

				if (!team_formed)
					break; // Cannot form more teams
			}

			if (teams_formed == 2)
			{
				// Add new teams to teamdata and send messages
				for (auto& new_team : new_teams)
				{
					teamdata.push_back(new_team);

					// Send messages to the players
					for (int j = 0; j < 6; ++j)
					{
						int role_id = new_team.GET_ROLE(j);
						if (role_id >= DEF_ROLEID)
							LuaManager::GetInstance()->ArenaPlayerMessage(role_id, MSG_ADD_USER_SUCCES);
						if (this->log) printf("ARENA::CreateRandom6x6Teams: Formed new random team with roleid = %d\n", role_id);
					}
				}
			}
			else
			{
				// Restore original cls_roles if two teams could not be formed
				cls_roles = original_cls_roles;
			}

			// Collect remaining roles
			std::vector<int> remaining_roles;
			for (auto& pair : cls_roles)
				remaining_roles.insert(remaining_roles.end(), pair.second.begin(), pair.second.end());

			random_role_6x6 = remaining_roles;
		}
		pthread_mutex_unlock(&arena_mutex);
	}

	
	void CelestialBattleManager::BattleRelease(int battle , int id1, int id2, char mode)
	{
		LuaManager * lua = LuaManager::GetInstance();
		int idx = ( battle | ((tick % 0xffff)<<16));
		ArenaBattleStart abs(idx,mode);

		// Create thread data structures
    	BattleReleaseThreadData data1{this, &abs, id1, idx, mode, lua, true};
    	BattleReleaseThreadData data2{this, &abs, id2, idx, mode, lua, false};

    	pthread_t t1, t2;
    	pthread_create(&t1, nullptr, ProcessTeamBattleRelease, &data1);
    	pthread_create(&t2, nullptr, ProcessTeamBattleRelease, &data2);

    	pthread_join(t1, nullptr);
    	pthread_join(t2, nullptr);

		if( abs.src.size() && abs.dst.size() )
		GProviderServer::GetInstance()->DispatchProtocol( 1 , abs );
	
		if( this->log ) printf("ARENA::BattleRelease: id1 = %d , id2 = %d , battle_idx = %d , battle_mode = %d , src_count = %d , dst_count = %d \n", 
								id1 , id2 , abs.id , abs.mode , abs.src.size() , abs.dst.size() );
	}
	
	void CelestialBattleManager::BattleStart()
	{
		pthread_mutex_lock(&arena_mutex);
		
		if ( v1x1.size() >= 2 && CountTeamJoin() >= 2 ) 
		{
			for (unsigned int i = 0; i < (v1x1.size()/2) && i < (MAX_TEAM/2); i++)
			{
				BattleRelease( (i + MAX_TEAM*0) , v1x1[0] , v1x1[1] , MODE_1X1);
				v1x1.erase(v1x1.begin(), v1x1.begin() + 2);
			}
		}
		
		if ( v3x3.size() >= 2 && CountTeamJoin() >= 2 ) 
		{
			for (unsigned int i = 0; i < (v3x3.size()/2) && i < (MAX_TEAM/2); i++)
			{
				BattleRelease( (i + MAX_TEAM*1) , v3x3[0] , v3x3[1] , MODE_3X3);
				v3x3.erase(v3x3.begin(), v3x3.begin() + 2);
			}
		}		
		
		if ( v6x6.size() >= 2 && CountTeamJoin() >= 2 ) 
		{
			for (unsigned int i = 0; i < (v6x6.size()/2) && i < (MAX_TEAM/2); i++)
			{
				BattleRelease( (i + MAX_TEAM*2) , v6x6[0] , v6x6[1] , MODE_6X6);
				v6x6.erase(v6x6.begin(), v6x6.begin() + 2);
			}
		}	
		
		pthread_mutex_unlock(&arena_mutex);
		if( this->log ) printf("ARENA::BattleStart: 1x1Count = %d , 3x3Count = %d , 6x6Count = %d , Count = %d , CountJoin = %d , CountBattle = %d , CountEnd = %d \n", 
								v1x1.size() , v3x3.size() , v6x6.size(), teamdata.size() , CountTeamJoin() , CountTeamBattle() , CountTeamEnd() );
	}

	void CelestialBattleManager::RandomBattleStart()
    {
        pthread_mutex_lock(&arena_mutex);

        if ( random_team_3x3.size() >= 2 ) 
        {
            for (unsigned int i = 0; i < (random_team_3x3.size()/2) && i < (MAX_TEAM/2); i++)
            {
                BattleRelease( (i + MAX_TEAM*1 + 1000) , random_team_3x3[0] , random_team_3x3[1] , MODE_3X3);
                random_team_3x3.erase(random_team_3x3.begin(), random_team_3x3.begin() + 2);
            }
        }   

		if ( random_team_6x6.size() >= 2 ) 
        {
            for (unsigned int i = 0; i < (random_team_6x6.size()/2) && i < (MAX_TEAM/2); i++)
            {
                BattleRelease( (i + MAX_TEAM*1 + 1000) , random_team_6x6[0] , random_team_6x6[1] , MODE_6X6);
                random_team_6x6.erase(random_team_6x6.begin(), random_team_6x6.begin() + 2);
            }
        }

        pthread_mutex_unlock(&arena_mutex);
        if( this->log ) printf("ARENA::RandomBattleStart: random_team_3x3_count = %d, random_team_6x6_count = %d , Count = %d , CountJoin = %d , CountBattle = %d , CountEnd = %d \n", 
                                (int)random_team_3x3.size(), (int)random_team_6x6.size(), (int)teamdata.size() , CountTeamJoin() , CountTeamBattle() , CountTeamEnd() );
    }

	void CelestialBattleManager::BattleResult(int roleid, char battle_mode, char is_win, int score)
	{
		int add_score = 0;
		int add_score_team = 0;
		
		switch (battle_mode)
		{
		case MODE_1X1:
			{
				is_win ? add_score += score : add_score -= score;
			}
			break;
		case MODE_3X3:
			{
				is_win ? add_score	+= (score/3) : add_score -= (score/3);
				is_win ? add_score_team += ((score/3)/3) : add_score_team -= ((score/3)/4);
			}
			break;
		case MODE_6X6:
			{
				is_win ? add_score	+= (score/6) : add_score -= (score/6);
				is_win ? add_score_team += ((score/6)/2) : add_score_team -= ((score/6)/3);
			}
			break;
		default:
			return;
			break;
		}
		
		LuaManager * lua = LuaManager::GetInstance();
		add_score *= lua->GetConfig()->ARENA_RATE;
		add_score_team *= lua->GetConfig()->ARENA_RATE_TEAM;
		
		ArenaBattleResult abr(roleid, battle_mode, is_win, add_score, add_score_team);
		PlayerInfo* pInfo = UserContainer::GetInstance().FindRoleOnline( roleid );
		if( pInfo && pInfo->user->gameid > 1)
		{
			GProviderServer::GetInstance()->DispatchProtocol( pInfo->user->gameid , abr );
		}
		else
		{
			if (is_win < 2)
			{
				GameDBClient::GetInstance()->SendProtocol(abr);
			}
		}
		CelestialBattleTimerManager::GetInstance()->AddRole(roleid);
	}
	

	void CelestialBattleManager::BattleEnd(int battle_id, char battle_mode, char red_win, int score, int damage_red, int damage_blue)
	{
		pthread_mutex_lock(&arena_mutex);
		LuaManager * lua = LuaManager::GetInstance();
		int tick_end = 0;
		for ( unsigned int i = 0; i < teamdata.size() && i < MAX_TEAM; i++ )
		{
			if( teamdata.at(i).battle_id == battle_id )
			{
				if (red_win == FLAG_RED || red_win == FLAG_BLUE)
				{
					if (teamdata.at(i).red == red_win)
					{
						for ( unsigned int j = 0; j < teamdata.at(i).COUNT_ROLE() && j < teamdata.at(i).GET_MAX_PLAYERS(); j++ )
						{
							int roleid = teamdata.at(i).GET_ROLE(j);
							if(roleid >= DEF_ROLEID)
							lua->ArenaPlayerMessage(roleid, MSG_BATTLE_USER_WIN);
							BattleResult(roleid, battle_mode, 1, score);
						}
					}
					if (teamdata.at(i).red != red_win)
					{
						for ( unsigned int j = 0; j < teamdata.at(i).COUNT_ROLE() && j < teamdata.at(i).GET_MAX_PLAYERS(); j++ )
						{
							int roleid = teamdata.at(i).GET_ROLE(j);
							if(roleid >= DEF_ROLEID)
							lua->ArenaPlayerMessage(roleid, MSG_BATTLE_USER_LOSE);
							BattleResult(roleid, battle_mode, 0, score);
						}
					}
				}
				else
				{
					for ( unsigned int j = 0; j < teamdata.at(i).COUNT_ROLE() && j < teamdata.at(i).GET_MAX_PLAYERS(); j++ )
					{
						int roleid = teamdata.at(i).GET_ROLE(j);
						if(roleid >= DEF_ROLEID)
						lua->ArenaPlayerMessage(roleid, MSG_BATTLE_USER_DRAW);
						BattleResult(roleid, battle_mode, 3, score);
					}
				}
				tick_end = (int)teamdata.at(i).tick;
				teamdata.at(i).tick = 60;
				teamdata.at(i).state = STATE_END;
				if( this->log )printf("ARENA::BattleEnd: team_id = %d , red = %d , red_win = %d, tick_end = %d \n", teamdata.at(i).id , teamdata.at(i).red , red_win, tick_end);
			}
		}
		pthread_mutex_unlock(&arena_mutex);
		if( this->log ) printf("ARENA::BattleEnd: battle_id = %d , red_win = %d \n", battle_id , red_win );
		SendEndDiscordWebHook(battle_id, battle_mode, red_win, tick_end, damage_red, damage_blue);
	}

	void CelestialBattleManager::SendEndDiscordWebHook(int battle_id, char battle_mode, char red_win, int tick_end, int damage_red, int damage_blue)
	{
		LuaManager * lua = LuaManager::GetInstance();
		std::vector<int> redTeamRoles = GetRedTeamRoles(battle_id);
		std::vector<int> blueTeamRoles = GetBlueTeamRoles(battle_id);

		int result = red_win == FLAG_RED ? 1 : red_win == FLAG_BLUE ? 2 : 0;

		switch(battle_mode)
		{
			case MODE_1X1:
				if (redTeamRoles.size() > 0 && blueTeamRoles.size() > 0)
				{
					lua->sendArena1x1EndDiscordWebHook(redTeamRoles[0], blueTeamRoles[0], result, tick_end, damage_red, damage_blue);
				}
				break;
			case MODE_3X3:
				if (redTeamRoles.size() >= 3 && blueTeamRoles.size() >= 3)
				{
					lua->sendArena3x3EndDiscordWebHook(
						redTeamRoles[0], redTeamRoles[1], redTeamRoles[2],
						blueTeamRoles[0], blueTeamRoles[1], blueTeamRoles[2],
						result,
						tick_end
					);
				}
				break;
			case MODE_6X6:
				if (redTeamRoles.size() >= 6 && blueTeamRoles.size() >= 6)
				{
					lua->sendArena6x6EndDiscordWebHook(
						redTeamRoles[0], redTeamRoles[1], redTeamRoles[2], redTeamRoles[3], redTeamRoles[4], redTeamRoles[5],
						blueTeamRoles[0], blueTeamRoles[1], blueTeamRoles[2], blueTeamRoles[3], blueTeamRoles[4], blueTeamRoles[5],
						result,
						tick_end
					);
				}
				break;
			default:
				break;
		}
	}
	void CelestialBattleManager::Update()
	{
		AutoClean();
		if( CountTeamJoin() >= 2 )
		{
			SetPool();
			BattleStart();
		}

		RandomCreateTeams();
		RandomSetPool();
		RandomBattleStart();
	}
	
	void CelestialBattleManager::Init()
	{
		pthread_mutex_init(&arena_mutex,0);
		pthread_mutex_lock(&arena_mutex);
		log = true;
		tick = 0;
		team_counter = 0;
		teamdata.clear();
		teamdata.reserve(MAX_TEAM);
		random_role_3x3_ticks.clear();
		random_role_6x6_ticks.clear();
		pthread_mutex_unlock(&arena_mutex);
	}

	void CelestialBattleManager::CheckAndEndInactiveBattles()
	{
		//pthread_mutex_lock(&arena_mutex);
		LuaManager *lua = LuaManager::GetInstance();

		for (auto &team : teamdata)
		{
			//printf("Verificando team.battle_id = %d, team.state = %d, team.tick = %d, check_time = %d \n", team.battle_id, team.state, team.tick, lua->GetConfig()->ARENA_BATTLE_TIME - 60);
			if (team.state == STATE_BATTLE && team.tick == lua->GetConfig()->ARENA_BATTLE_TIME - 60)
			{
				std::vector<int> blueTeamRoles = GetBlueTeamRoles(team.battle_id);
				std::vector<int> redTeamRoles = GetRedTeamRoles(team.battle_id);

				 //printf("Verificando equipe azul e vermelha para battle_id = %d\n", team.battle_id);

				bool allBlueInactive = std::all_of(blueTeamRoles.begin(), blueTeamRoles.end(), [lua](int roleid) {
					bool inactive = !lua->game__CheckRoleGsArena(roleid);
					printf("Blue roleid = %d, inactive = %d\n", roleid, inactive);
					return inactive;
				});

				bool allRedInactive = std::all_of(redTeamRoles.begin(), redTeamRoles.end(), [lua](int roleid) {
					bool inactive = !lua->game__CheckRoleGsArena(roleid);
					//printf("Red roleid = %d, inactive = %d\n", roleid, inactive);
					return inactive;
				});

				if (allBlueInactive && allRedInactive)
				{
					printf("Finalizando batalha para battle_id = %d\n", team.battle_id);
					BattleEnd(team.battle_id, team.mode, 0xFF, 0, 0, 0);
				}
			}
		}

		//pthread_mutex_unlock(&arena_mutex);
	}

	
	void CelestialBattleManager::HeartBeat()
	{
		printf("ARENA::HeartBeat: tick = %d \n", tick);
		pthread_mutex_lock(&arena_mutex);
		LuaManager * lua = LuaManager::GetInstance();
		
		for ( unsigned int i = 0; i < teamdata.size() && i < MAX_TEAM; i++ )
		{
			if( teamdata.at(i).tick-- <= 1 || !teamdata.at(i).COUNT_ROLE() )
			{
				if( teamdata.at(i).state == STATE_JOIN )
				{
					for ( unsigned int j = 0; j < teamdata.at(i).COUNT_ROLE() && j < teamdata.at(i).GET_MAX_PLAYERS(); j++ )
					{
						int roleid = teamdata.at(i).GET_ROLE(j);
						if( roleid >= DEF_ROLEID)
						{
							lua->ArenaPlayerMessage( roleid , MSG_DEL_USER_TIMEOUT);
						}
					}
				}
				teamdata.at(i).CLEAR_ROLELIST();
				teamdata.erase(teamdata.begin() + i);
			}
			else
			{
				if(!(teamdata.at(i).tick % lua->GetConfig()->ARENA_MSG_TIME) && teamdata.at(i).state == STATE_JOIN )
				{
					for ( unsigned int j = 0; j < teamdata.at(i).COUNT_ROLE() && j < teamdata.at(i).GET_MAX_PLAYERS(); j++ )
					{
						int roleid = teamdata.at(i).GET_ROLE(j);
						if( roleid >= DEF_ROLEID )
						lua->ArenaTimerCounter( roleid , teamdata.at(i).tick);
					}
				}
			}
		}

		for (auto it = random_role_3x3_ticks.begin(); it != random_role_3x3_ticks.end();)
		{
			if (--it->second <= 1)
			{
				//DelSoloRole3x3(it->first);
				lua->ArenaPlayerMessage(it->first, MSG_DEL_TEAM_USER_TIMEOUT);

				auto vec_it = std::find(random_role_3x3.begin(), random_role_3x3.end(), it->first);
				if (vec_it != random_role_3x3.end())
				{
					random_role_3x3.erase(vec_it);
				}

				it = random_role_3x3_ticks.erase(it);
			}
			else
			{
				if (!(it->second % lua->GetConfig()->ARENA_MSG_TIME))
				{
					lua->ArenaTeamTimerCounter(it->first, it->second);
				}
				++it;
			}
		}

		for (auto it = random_role_6x6_ticks.begin(); it != random_role_6x6_ticks.end();)
		{
			if (--it->second <= 1)
			{
				//DelSoloRole6x6(it->first);
				lua->ArenaPlayerMessage(it->first, MSG_DEL_TEAM_USER_TIMEOUT);
				
				auto vec_it = std::find(random_role_6x6.begin(), random_role_6x6.end(), it->first);
				if (vec_it != random_role_6x6.end())
				{
					random_role_6x6.erase(vec_it);
				}

				it = random_role_6x6_ticks.erase(it);
			}
			else
			{
				if (!(it->second % lua->GetConfig()->ARENA_MSG_TIME))
				{
					lua->ArenaTeamTimerCounter(it->first, it->second);
				}
				++it;
			}
		}
		
		pthread_mutex_unlock(&arena_mutex);

		CheckAndEndInactiveBattles();
		
		if(!(tick++ % lua->GetConfig()->ARENA_UPDATE_TIME))
		{
			Update();
		}
		
		if( this->log && (teamdata.size() || random_role_3x3.size() || random_role_6x6.size()) ) printf("ARENA::Heartbeat: tick = %d , Count = %d , CountJoin = %d , CountBattle = %d , CountEnd = %d, random_role_3x3 = %d, random_role_6x6 = %d \n", 
													tick , teamdata.size() , CountTeamJoin() , CountTeamBattle() , CountTeamEnd(), random_role_3x3.size(), random_role_6x6.size() );
	}	
	
	/*Timer task*/
	void ArenaTimer::UpdateTimer()
	{
		printf("ARENA::UpdateTimer: ArenaTimer::UpdateTimer() \n");
		CelestialBattleTimerManager::GetInstance()->HeartBeat();
		CelestialBattleManager::GetInstance()->HeartBeat();
	}

	void ArenaTimer::Run()
	{
		printf("ARENA::Run: ArenaTimer::Run() \n");
		UpdateTimer();
		Thread::HouseKeeper::AddTimerTask(this,update_time);
	}
};

